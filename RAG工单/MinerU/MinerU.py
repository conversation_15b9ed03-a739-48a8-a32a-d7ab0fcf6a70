# -*- coding: utf-8 -*-
import requests

url='https://mineru.net/api/v4/extract/task'
header = {
    'Content-Type':'application/json',
    'Authorization':'Bearer eyJ0eXBlIjoiSldUIiwiYWxnIjoiSFM1MTIifQ.eyJqdGkiOiI5MDMwODIwOCIsInJvbCI6IlJPTEVfUkVHSVNURVIiLCJpc3MiOiJPcGVuWExhYiIsImlhdCI6MTc1Mzg0MjczNiwiY2xpZW50SWQiOiJsa3pkeDU3bnZ5MjJqa3BxOXgydyIsInBob25lIjoiIiwib3BlbklkIjpudWxsLCJ1dWlkIjoiOTNiZmMxZTUtMjcwZC00NDAxLTlkYzEtMGZlMDMwMmQzMjc1IiwiZW1haWwiOiIiLCJleHAiOjE3NTUwNTIzMzZ9.93-dqKhNYVncQTGeZDR88bwxXTkDw1yi_eeqcSjW1D26k6PHTMa3Xf_om7O05t2cQB_MpVaOD3PHmKpobWrDow'
}
data = {
    'url':'https://cdn-mineru.openxlab.org.cn/demo/example.pdf',
    'is_ocr':True,
    'enable_formula': False,
}

res = requests.post(url,headers=header,json=data)
print(res.status_code)
print(res.json())
print(res.json()["data"])


