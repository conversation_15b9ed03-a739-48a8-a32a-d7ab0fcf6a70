# -*- coding: utf-8 -*-
"""
错误处理和日志模块
提供统一的错误处理、日志记录和异常管理功能
"""

import logging
import traceback
import functools
import time
from typing import Any, Callable, Dict, Optional, Union
from datetime import datetime
import json
import os
from logging.handlers import RotatingFileHandler
from config import Config

class CustomFormatter(logging.Formatter):
    """
    自定义日志格式化器
    支持彩色输出和结构化日志
    """
    
    # 颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        """
        格式化日志记录
        
        Args:
            record: 日志记录对象
            
        Returns:
            格式化后的日志字符串
        """
        # 添加颜色
        if hasattr(record, 'levelname'):
            color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
            record.levelname = f"{color}{record.levelname}{self.COLORS['RESET']}"
        
        # 添加额外信息
        if not hasattr(record, 'module_name'):
            record.module_name = record.name.split('.')[-1]
        
        if not hasattr(record, 'function_name'):
            record.function_name = record.funcName or 'unknown'
        
        # 格式化时间
        record.asctime = datetime.fromtimestamp(record.created).strftime('%Y-%m-%d %H:%M:%S')
        
        return super().format(record)

class ErrorHandler:
    """
    错误处理器
    提供统一的错误处理和日志记录功能
    """
    
    def __init__(self):
        """
        初始化错误处理器
        """
        self.logger = self._setup_logger()
        self.error_stats = {
            'total_errors': 0,
            'error_types': {},
            'last_error_time': None
        }
    
    def _setup_logger(self) -> logging.Logger:
        """
        设置日志记录器
        
        Returns:
            配置好的日志记录器
        """
        logger = logging.getLogger('RAG_System')
        logger.setLevel(getattr(logging, Config.LOG_LEVEL))
        
        # 避免重复添加处理器
        if logger.handlers:
            return logger
        
        # 创建格式化器
        formatter = CustomFormatter(
            '%(asctime)s - %(module_name)s.%(function_name)s - %(levelname)s - %(message)s'
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # 文件处理器
        if Config.LOG_FILE:
            # 确保日志目录存在
            log_dir = os.path.dirname(Config.LOG_FILE)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir)
            
            file_handler = RotatingFileHandler(
                Config.LOG_FILE,
                maxBytes=Config.LOG_MAX_SIZE,
                backupCount=Config.LOG_BACKUP_COUNT,
                encoding='utf-8'
            )
            file_handler.setLevel(logging.DEBUG)
            
            # 文件日志使用简单格式（无颜色）
            file_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)
        
        return logger
    
    def log_error(self, error: Exception, context: Optional[Dict] = None, 
                  level: str = 'ERROR') -> str:
        """
        记录错误信息
        
        Args:
            error: 异常对象
            context: 错误上下文信息
            level: 日志级别
            
        Returns:
            错误ID
        """
        error_id = f"ERR_{int(time.time())}_{id(error)}"
        error_type = type(error).__name__
        
        # 更新错误统计
        self.error_stats['total_errors'] += 1
        self.error_stats['error_types'][error_type] = (
            self.error_stats['error_types'].get(error_type, 0) + 1
        )
        self.error_stats['last_error_time'] = datetime.now()
        
        # 构建错误信息
        error_info = {
            'error_id': error_id,
            'error_type': error_type,
            'error_message': str(error),
            'traceback': traceback.format_exc(),
            'context': context or {},
            'timestamp': datetime.now().isoformat()
        }
        
        # 记录日志
        log_message = f"[{error_id}] {error_type}: {str(error)}"
        if context:
            log_message += f" | Context: {json.dumps(context, ensure_ascii=False)}"
        
        getattr(self.logger, level.lower())(log_message)
        
        # 详细日志记录到文件
        if Config.ENABLE_DETAILED_LOGGING:
            self.logger.debug(f"详细错误信息: {json.dumps(error_info, ensure_ascii=False, indent=2)}")
        
        return error_id
    
    def log_info(self, message: str, context: Optional[Dict] = None):
        """
        记录信息日志
        
        Args:
            message: 日志消息
            context: 上下文信息
        """
        if context:
            message += f" | Context: {json.dumps(context, ensure_ascii=False)}"
        self.logger.info(message)
    
    def log_warning(self, message: str, context: Optional[Dict] = None):
        """
        记录警告日志
        
        Args:
            message: 日志消息
            context: 上下文信息
        """
        if context:
            message += f" | Context: {json.dumps(context, ensure_ascii=False)}"
        self.logger.warning(message)
    
    def log_debug(self, message: str, context: Optional[Dict] = None):
        """
        记录调试日志
        
        Args:
            message: 日志消息
            context: 上下文信息
        """
        if context:
            message += f" | Context: {json.dumps(context, ensure_ascii=False)}"
        self.logger.debug(message)
    
    def get_error_stats(self) -> Dict:
        """
        获取错误统计信息
        
        Returns:
            错误统计字典
        """
        return self.error_stats.copy()

# 全局错误处理器实例
error_handler = ErrorHandler()

def handle_exceptions(fallback_return=None, log_level='ERROR'):
    """
    异常处理装饰器
    自动捕获和记录函数执行中的异常
    
    Args:
        fallback_return: 异常时的返回值
        log_level: 日志级别
        
    Returns:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # 记录错误
                context = {
                    'function': func.__name__,
                    'module': func.__module__,
                    'args': str(args)[:200],  # 限制长度
                    'kwargs': str(kwargs)[:200]
                }
                
                error_id = error_handler.log_error(e, context, log_level)
                
                # 返回回退值
                if fallback_return is not None:
                    return fallback_return
                else:
                    # 重新抛出异常
                    raise
        
        return wrapper
    return decorator

def log_execution_time(func: Callable) -> Callable:
    """
    执行时间记录装饰器
    记录函数的执行时间
    
    Args:
        func: 要装饰的函数
        
    Returns:
        装饰后的函数
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            # 记录执行时间
            context = {
                'function': func.__name__,
                'execution_time': f"{execution_time:.3f}s"
            }
            
            if execution_time > 5.0:  # 超过5秒记录警告
                error_handler.log_warning(f"函数执行时间较长: {func.__name__}", context)
            else:
                error_handler.log_debug(f"函数执行完成: {func.__name__}", context)
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            context = {
                'function': func.__name__,
                'execution_time': f"{execution_time:.3f}s",
                'error': str(e)
            }
            error_handler.log_error(e, context)
            raise
    
    return wrapper

class PerformanceMonitor:
    """
    性能监控器
    监控系统性能指标
    """
    
    def __init__(self):
        """
        初始化性能监控器
        """
        self.metrics = {
            'function_calls': {},
            'execution_times': {},
            'memory_usage': [],
            'error_rates': {}
        }
    
    def record_function_call(self, func_name: str, execution_time: float, 
                           success: bool = True):
        """
        记录函数调用
        
        Args:
            func_name: 函数名
            execution_time: 执行时间
            success: 是否成功
        """
        if func_name not in self.metrics['function_calls']:
            self.metrics['function_calls'][func_name] = {
                'total_calls': 0,
                'successful_calls': 0,
                'failed_calls': 0,
                'avg_execution_time': 0.0
            }
        
        stats = self.metrics['function_calls'][func_name]
        stats['total_calls'] += 1
        
        if success:
            stats['successful_calls'] += 1
        else:
            stats['failed_calls'] += 1
        
        # 更新平均执行时间
        if func_name not in self.metrics['execution_times']:
            self.metrics['execution_times'][func_name] = []
        
        self.metrics['execution_times'][func_name].append(execution_time)
        
        # 保持最近100次记录
        if len(self.metrics['execution_times'][func_name]) > 100:
            self.metrics['execution_times'][func_name] = (
                self.metrics['execution_times'][func_name][-100:]
            )
        
        # 计算平均执行时间
        times = self.metrics['execution_times'][func_name]
        stats['avg_execution_time'] = sum(times) / len(times)
    
    def get_performance_report(self) -> Dict:
        """
        获取性能报告
        
        Returns:
            性能报告字典
        """
        report = {
            'timestamp': datetime.now().isoformat(),
            'function_stats': self.metrics['function_calls'].copy(),
            'system_stats': {
                'total_functions_monitored': len(self.metrics['function_calls']),
                'total_calls': sum(
                    stats['total_calls'] 
                    for stats in self.metrics['function_calls'].values()
                ),
                'total_errors': sum(
                    stats['failed_calls'] 
                    for stats in self.metrics['function_calls'].values()
                )
            }
        }
        
        return report

# 全局性能监控器实例
performance_monitor = PerformanceMonitor()

def monitor_performance(func: Callable) -> Callable:
    """
    性能监控装饰器
    
    Args:
        func: 要监控的函数
        
    Returns:
        装饰后的函数
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        success = True
        
        try:
            result = func(*args, **kwargs)
            return result
        except Exception as e:
            success = False
            raise
        finally:
            execution_time = time.time() - start_time
            performance_monitor.record_function_call(
                func.__name__, execution_time, success
            )
    
    return wrapper

# 便捷函数
def log_info(message: str, context: Optional[Dict] = None):
    """记录信息日志"""
    error_handler.log_info(message, context)

def log_warning(message: str, context: Optional[Dict] = None):
    """记录警告日志"""
    error_handler.log_warning(message, context)

def log_error(error: Exception, context: Optional[Dict] = None) -> str:
    """记录错误日志"""
    return error_handler.log_error(error, context)

def log_debug(message: str, context: Optional[Dict] = None):
    """记录调试日志"""
    error_handler.log_debug(message, context)
