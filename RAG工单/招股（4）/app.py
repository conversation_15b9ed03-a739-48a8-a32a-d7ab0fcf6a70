import os
import uuid
import logging
from flask import Flask, request, jsonify, render_template, session
from flask_cors import CORS
from file_processor import allowed_file, process_uploaded_file
from vector_search import search_similar
from llm_client import generate_answer
from embeddings import generate_embeddings, init_embedding_model
from milvus_manager import init_milvus, store_vectors
from llm_client import init_llm_client
from reranker import rerank_documents
from redis_manager import redis_manager  # 引入Redis管理器
from speech_processor import SpeechProcessor  # 新增语音处理模块
from ner_classifier import NERClassifier  # 新增命名实体识别和分类模块

app = Flask(__name__)
CORS(app)
app.secret_key = os.urandom(24)  # 设置会话密钥

# 配置
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {
    'txt', 'pdf', 'docx', 'doc',           # 文档类型
    'jpg', 'jpeg', 'png', 'bmp', 'tiff',   # 图片类型
    'xlsx', 'xls', 'csv',                  # 表格类型
    'wav', 'mp3', 'm4a', 'flac', 'aac'     # 音频类型
}
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 50 * 1024 * 1024  # 50MB 最大文件大小

# 确保上传文件夹存在
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# 初始化日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 全局组件
collection = None
embedding_model = None
llm_client = None
speech_processor = None
ner_classifier = None
hybrid_search_engine = None


def initialize_application():
    """
    初始化应用程序所有组件
    包括向量数据库、嵌入模型、LLM客户端、语音处理器、NER分类器等

    Returns:
        bool: 初始化是否成功
    """
    global collection, embedding_model, llm_client, speech_processor, ner_classifier, hybrid_search_engine
    try:
        logger.info("正在初始化应用程序组件...")

        # 初始化核心组件
        collection = init_milvus()
        embedding_model = init_embedding_model()
        llm_client = init_llm_client()

        # 初始化增强功能组件
        try:
            from speech_processor import init_speech_processor
            speech_processor = init_speech_processor()
            logger.info("语音处理器初始化成功")
        except Exception as e:
            logger.warning(f"语音处理器初始化失败: {str(e)}")
            speech_processor = None

        try:
            from ner_classifier import init_ner_classifier
            ner_classifier = init_ner_classifier()
            logger.info("NER分类器初始化成功")
        except Exception as e:
            logger.warning(f"NER分类器初始化失败: {str(e)}")
            ner_classifier = None

        try:
            from vector_search import init_hybrid_search_engine
            hybrid_search_engine = init_hybrid_search_engine()
            logger.info("混合检索引擎初始化成功")
        except Exception as e:
            logger.warning(f"混合检索引擎初始化失败: {str(e)}")
            hybrid_search_engine = None

        logger.info("所有组件初始化完成")
        return True

    except Exception as e:
        logger.error(f"组件初始化失败: {str(e)}", exc_info=True)
        return False


# 在应用启动前初始化组件
initialize_application()


# 路由
@app.route('/')
def index():
    # 为新访客生成会话ID
    if 'session_id' not in session:
        session['session_id'] = str(uuid.uuid4())
        logger.info(f"新会话开始: {session['session_id']}")
    return render_template('index.html')


@app.route('/upload', methods=['POST'])
def upload_file():
    """
    处理文件上传
    支持文档、图片、表格、音频等多种文件类型

    Returns:
        JSON响应，包含处理结果
    """
    try:
        # 检查文件是否存在
        if 'file' not in request.files:
            return jsonify({"status": "error", "message": "没有文件部分"})

        file = request.files['file']
        if file.filename == '':
            return jsonify({"status": "error", "message": "没有选择文件"})

        # 检查文件类型
        if not file or not allowed_file(file.filename, ALLOWED_EXTENSIONS):
            return jsonify({
                "status": "error",
                "message": f"不支持的文件类型。支持的类型: {', '.join(sorted(ALLOWED_EXTENSIONS))}"
            })

        # 检查文件大小
        file.seek(0, 2)  # 移动到文件末尾
        file_size = file.tell()
        file.seek(0)  # 重置到文件开头

        if file_size > app.config['MAX_CONTENT_LENGTH']:
            return jsonify({
                "status": "error",
                "message": f"文件过大，最大支持 {app.config['MAX_CONTENT_LENGTH'] // (1024*1024)}MB"
            })

        # 获取文件类型
        from file_processor import EnhancedFileProcessor
        processor = EnhancedFileProcessor()
        file_type = processor.get_file_type(file.filename)

        # 处理音频文件
        if file_type == 'unknown' and processor.is_audio_file(file.filename):
            return handle_audio_upload(file)

        # 处理其他文件类型
        try:
            # 使用增强的文件处理
            from file_processor import process_uploaded_file_enhanced
            result = process_uploaded_file_enhanced(
                file,
                app.config['UPLOAD_FOLDER'],
                collection,
                generate_embeddings
            )

            # 添加文件信息到响应
            result['file_info'] = {
                'filename': file.filename,
                'file_type': file_type,
                'file_size': file_size
            }

            return jsonify(result)

        except Exception as e:
            logger.error(f"增强文件处理失败: {str(e)}", exc_info=True)

            # 回退到基础处理
            try:
                from file_processor import process_uploaded_file
                result = process_uploaded_file(
                    file,
                    app.config['UPLOAD_FOLDER'],
                    collection,
                    generate_embeddings
                )
                result['processing_method'] = 'fallback'
                return jsonify(result)

            except Exception as fallback_error:
                logger.error(f"基础文件处理也失败: {str(fallback_error)}", exc_info=True)
                return jsonify({
                    "status": "error",
                    "message": f"处理文件时出错: {str(fallback_error)}"
                })

    except Exception as e:
        logger.error(f"文件上传处理错误: {str(e)}", exc_info=True)
        return jsonify({"status": "error", "message": f"上传处理失败: {str(e)}"})

def handle_audio_upload(file):
    """
    处理音频文件上传

    Args:
        file: 音频文件对象

    Returns:
        JSON响应
    """
    try:
        if not speech_processor:
            return jsonify({
                "status": "error",
                "message": "语音处理功能未启用"
            })

        # 保存临时文件
        import tempfile
        import os

        with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1]) as temp_file:
            file.save(temp_file.name)

            # 语音转文字
            text = speech_processor.speech_to_text(temp_file.name)

            # 删除临时文件
            os.unlink(temp_file.name)

        if not text:
            return jsonify({
                "status": "error",
                "message": "语音识别失败，无法提取文字内容"
            })

        # 将识别的文字作为文本处理
        from file_processor import SmartTextChunker
        chunker = SmartTextChunker()
        chunks_with_metadata = chunker.chunk_text_smart(text)

        # 提取纯文本用于嵌入
        chunk_texts = [chunk["text"] for chunk in chunks_with_metadata]

        # 生成嵌入向量
        embeddings = generate_embeddings(chunk_texts)

        # 存储向量
        from milvus_manager import store_vectors_with_metadata
        try:
            ids = store_vectors_with_metadata(chunks_with_metadata, embeddings, collection)
        except:
            from milvus_manager import store_vectors
            ids = store_vectors(chunk_texts, embeddings, collection)

        return jsonify({
            "status": "success",
            "message": f"音频文件处理成功，识别文字并存储 {len(chunks_with_metadata)} 个片段",
            "chunks_count": len(chunks_with_metadata),
            "file_type": "audio",
            "recognized_text": text[:200] + "..." if len(text) > 200 else text
        })

    except Exception as e:
        logger.error(f"音频文件处理失败: {str(e)}", exc_info=True)
        return jsonify({
            "status": "error",
            "message": f"音频处理失败: {str(e)}"
        })


@app.route('/query', methods=['POST'])
def handle_query():
    """
    处理用户查询
    集成NER分类、混合检索、智能重排序和增强回答生成

    Returns:
        JSON响应，包含答案和相关信息
    """
    try:
        # 解析请求数据
        data = request.json
        if not data or 'query' not in data:
            return jsonify({"status": "error", "message": "缺少查询内容"})

        # 获取会话ID
        if 'session_id' not in session:
            session['session_id'] = str(uuid.uuid4())
        session_id = session['session_id']

        # 获取历史对话
        chat_history = redis_manager.get_session(session_id)

        query_text = data['query'].strip()
        if not query_text:
            return jsonify({"status": "error", "message": "查询内容不能为空"})

        # 获取查询选项
        use_hybrid_search = data.get('use_hybrid_search', True)
        use_ner_classification = data.get('use_ner_classification', True)
        enable_tts = data.get('enable_tts', False)

        logger.info(f"处理查询: {query_text[:50]}...")

        # 1. NER分析和问题分类
        query_analysis = None
        if ner_classifier and use_ner_classification:
            try:
                query_analysis = ner_classifier.analyze_query(query_text)
                logger.info(f"查询分析完成: {query_analysis['classification']['category']}")
            except Exception as e:
                logger.warning(f"NER分析失败: {str(e)}")

        # 2. 混合检索
        try:
            if hybrid_search_engine and use_hybrid_search:
                initial_results = hybrid_search_engine.hybrid_search(
                    query_text, collection, generate_embeddings, top_k=20
                )
                search_method = "hybrid"
            else:
                # 回退到基础向量检索
                from vector_search import search_similar
                initial_results = search_similar(query_text, collection, generate_embeddings, top_k=15)
                search_method = "vector"

            logger.info(f"使用{search_method}检索，获得{len(initial_results)}个初始结果")

        except Exception as e:
            logger.error(f"检索失败: {str(e)}")
            initial_results = []

        # 检查是否有搜索结果
        if not initial_results:
            no_result_message = "没有找到相关信息来回答这个问题。请先上传相关文件。"

            # 添加到历史对话
            new_history = chat_history + [
                {"role": "user", "content": query_text},
                {"role": "assistant", "content": no_result_message}
            ]
            redis_manager.store_session(session_id, new_history)

            return jsonify({
                "status": "success",
                "session_id": session_id,
                "answer": no_result_message,
                "context": [],
                "search_method": search_method,
                "query_analysis": query_analysis
            })

        # 3. 文档重排序
        try:
            from reranker import rerank_documents
            reranked_results = rerank_documents(query_text, initial_results, top_n=8)
            logger.info(f"重排序完成，选择{len(reranked_results)}个最相关文档")
        except Exception as e:
            logger.warning(f"重排序失败: {str(e)}")
            reranked_results = initial_results[:8]

        # 4. 生成增强答案
        try:
            if query_analysis and ner_classifier:
                # 使用增强的提示词
                enhanced_prompt = ner_classifier.generate_enhanced_prompt(
                    query_text, reranked_results, query_analysis['classification']
                )

                # 使用增强提示词生成答案
                from llm_client import generate_answer_with_prompt
                try:
                    answer = generate_answer_with_prompt(enhanced_prompt, llm_client, chat_history)
                except:
                    # 回退到原始方法
                    answer = generate_answer(query_text, reranked_results, llm_client, chat_history)
            else:
                # 使用原始方法
                answer = generate_answer(query_text, reranked_results, llm_client, chat_history)

            logger.info("答案生成完成")

        except Exception as e:
            logger.error(f"答案生成失败: {str(e)}")
            answer = "抱歉，生成答案时出现错误，请稍后再试。"

        # 5. 语音合成（如果启用）
        audio_url = None
        if enable_tts and speech_processor:
            try:
                import os
                audio_filename = f"answer_{session_id}_{int(time.time())}.wav"
                audio_path = os.path.join("static", "audio", audio_filename)

                # 确保音频目录存在
                os.makedirs(os.path.dirname(audio_path), exist_ok=True)

                # 生成语音
                speech_processor.text_to_speech(answer, audio_path)
                audio_url = f"/static/audio/{audio_filename}"

            except Exception as e:
                logger.warning(f"语音合成失败: {str(e)}")

        # 6. 更新历史对话
        new_history = chat_history + [
            {"role": "user", "content": query_text},
            {"role": "assistant", "content": answer}
        ]
        redis_manager.store_session(session_id, new_history)

        # 7. 准备响应数据
        response_data = {
            "status": "success",
            "session_id": session_id,
            "answer": answer,
            "context": [doc.get("text", "") for doc in reranked_results[:5]],
            "search_method": search_method,
            "result_count": len(initial_results),
            "reranked_count": len(reranked_results)
        }

        # 添加查询分析结果
        if query_analysis:
            response_data["query_analysis"] = {
                "category": query_analysis['classification']['category'],
                "confidence": query_analysis['classification']['confidence'],
                "entities": query_analysis['entities'],
                "complexity": query_analysis['complexity']
            }

        # 添加参考文档信息
        if reranked_results:
            references = []
            for doc in reranked_results[:5]:
                ref = {
                    "text": doc.get("text", "")[:300] + "..." if len(doc.get("text", "")) > 300 else doc.get("text", ""),
                    "score": doc.get("score", 0),
                    "method": doc.get("method", "unknown")
                }

                # 添加重排序分数作为准确率
                if "rerank_score" in doc:
                    ref["accuracy"] = min(int(doc["rerank_score"] * 100), 100)
                else:
                    ref["accuracy"] = min(int(doc.get("score", 0) * 100), 100)

                references.append(ref)

            response_data["references"] = references

        # 添加音频URL
        if audio_url:
            response_data["audio_url"] = audio_url

        return jsonify(response_data)

    except Exception as e:
        logger.error(f"查询处理错误: {str(e)}", exc_info=True)
        return jsonify({
            "status": "error",
            "message": f"处理查询时出错: {str(e)}"
        })


@app.route('/speech-to-text', methods=['POST'])
def speech_to_text():
    """
    语音转文字API

    Returns:
        JSON响应，包含识别的文字
    """
    try:
        if not speech_processor:
            return jsonify({
                "status": "error",
                "message": "语音处理功能未启用"
            })

        # 检查音频文件
        if 'audio' not in request.files:
            return jsonify({
                "status": "error",
                "message": "没有音频文件"
            })

        audio_file = request.files['audio']
        if audio_file.filename == '':
            return jsonify({
                "status": "error",
                "message": "没有选择音频文件"
            })

        # 保存临时文件
        import tempfile
        import os

        with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as temp_file:
            audio_file.save(temp_file.name)

            # 语音识别
            text = speech_processor.speech_to_text(temp_file.name)

            # 删除临时文件
            os.unlink(temp_file.name)

        if text:
            return jsonify({
                "status": "success",
                "text": text
            })
        else:
            return jsonify({
                "status": "error",
                "message": "语音识别失败"
            })

    except Exception as e:
        logger.error(f"语音转文字失败: {str(e)}", exc_info=True)
        return jsonify({
            "status": "error",
            "message": f"语音转文字失败: {str(e)}"
        })


@app.route('/text-to-speech', methods=['POST'])
def text_to_speech():
    """
    文字转语音API

    Returns:
        音频文件或JSON响应
    """
    try:
        if not speech_processor:
            return jsonify({
                "status": "error",
                "message": "语音处理功能未启用"
            })

        data = request.json
        if not data or 'text' not in data:
            return jsonify({
                "status": "error",
                "message": "缺少文字内容"
            })

        text = data['text'].strip()
        if not text:
            return jsonify({
                "status": "error",
                "message": "文字内容不能为空"
            })

        # 生成音频文件
        import os
        import time

        audio_filename = f"tts_{int(time.time())}.wav"
        audio_path = os.path.join("static", "audio", audio_filename)

        # 确保音频目录存在
        os.makedirs(os.path.dirname(audio_path), exist_ok=True)

        # 生成语音
        result_path = speech_processor.text_to_speech(text, audio_path)

        if result_path:
            return jsonify({
                "status": "success",
                "audio_url": f"/static/audio/{audio_filename}"
            })
        else:
            return jsonify({
                "status": "error",
                "message": "语音合成失败"
            })

    except Exception as e:
        logger.error(f"文字转语音失败: {str(e)}", exc_info=True)
        return jsonify({
            "status": "error",
            "message": f"文字转语音失败: {str(e)}"
        })


@app.route('/system-status', methods=['GET'])
def system_status():
    """
    获取系统状态信息

    Returns:
        JSON响应，包含系统各组件状态
    """
    try:
        status = {
            "timestamp": time.time(),
            "components": {
                "milvus": collection is not None,
                "embedding_model": embedding_model is not None,
                "llm_client": llm_client is not None,
                "speech_processor": speech_processor is not None,
                "ner_classifier": ner_classifier is not None,
                "hybrid_search_engine": hybrid_search_engine is not None,
                "redis": redis_manager is not None
            },
            "statistics": {}
        }

        # 获取Milvus统计信息
        if collection:
            try:
                from milvus_manager import get_collection_stats
                milvus_stats = get_collection_stats(collection)
                status["statistics"]["milvus"] = milvus_stats
            except Exception as e:
                status["statistics"]["milvus"] = {"error": str(e)}

        # 获取Redis统计信息
        if redis_manager:
            try:
                redis_info = redis_manager.connection.info()
                status["statistics"]["redis"] = {
                    "connected_clients": redis_info.get("connected_clients", 0),
                    "used_memory": redis_info.get("used_memory_human", "unknown"),
                    "total_commands_processed": redis_info.get("total_commands_processed", 0)
                }
            except Exception as e:
                status["statistics"]["redis"] = {"error": str(e)}

        # 获取混合检索缓存统计
        if hybrid_search_engine:
            try:
                cache_stats = hybrid_search_engine.get_cache_stats()
                status["statistics"]["search_cache"] = cache_stats
            except Exception as e:
                status["statistics"]["search_cache"] = {"error": str(e)}

        return jsonify({
            "status": "success",
            "data": status
        })

    except Exception as e:
        logger.error(f"获取系统状态失败: {str(e)}", exc_info=True)
        return jsonify({
            "status": "error",
            "message": f"获取系统状态失败: {str(e)}"
        })


@app.route('/clear-cache', methods=['POST'])
def clear_cache():
    """
    清空系统缓存

    Returns:
        JSON响应
    """
    try:
        cleared_caches = []

        # 清空检索缓存
        if hybrid_search_engine:
            hybrid_search_engine.clear_cache()
            cleared_caches.append("search_cache")

        # 可以添加其他缓存清理逻辑

        return jsonify({
            "status": "success",
            "message": f"已清空缓存: {', '.join(cleared_caches)}"
        })

    except Exception as e:
        logger.error(f"清空缓存失败: {str(e)}", exc_info=True)
        return jsonify({
            "status": "error",
            "message": f"清空缓存失败: {str(e)}"
        })


@app.route('/chat-history/<session_id>', methods=['GET'])
def get_chat_history(session_id):
    """
    获取聊天历史

    Args:
        session_id: 会话ID

    Returns:
        JSON响应，包含聊天历史
    """
    try:
        history = redis_manager.get_session(session_id)
        return jsonify({
            "status": "success",
            "session_id": session_id,
            "history": history,
            "message_count": len(history)
        })

    except Exception as e:
        logger.error(f"获取聊天历史失败: {str(e)}", exc_info=True)
        return jsonify({
            "status": "error",
            "message": f"获取聊天历史失败: {str(e)}"
        })


@app.route('/clear-history/<session_id>', methods=['POST'])
def clear_chat_history(session_id):
    """
    清空聊天历史

    Args:
        session_id: 会话ID

    Returns:
        JSON响应
    """
    try:
        redis_manager.store_session(session_id, [])
        return jsonify({
            "status": "success",
            "message": "聊天历史已清空"
        })

    except Exception as e:
        logger.error(f"清空聊天历史失败: {str(e)}", exc_info=True)
        return jsonify({
            "status": "error",
            "message": f"清空聊天历史失败: {str(e)}"
        })


# 静态文件服务（用于音频文件）
@app.route('/static/audio/<filename>')
def serve_audio(filename):
    """
    提供音频文件服务

    Args:
        filename: 音频文件名

    Returns:
        音频文件响应
    """
    try:
        import os
        from flask import send_from_directory

        audio_dir = os.path.join("static", "audio")
        return send_from_directory(audio_dir, filename)

    except Exception as e:
        logger.error(f"音频文件服务失败: {str(e)}")
        return jsonify({
            "status": "error",
            "message": "音频文件不存在"
        }), 404


if __name__ == '__main__':
    # 确保必要的目录存在
    import os
    os.makedirs("static/audio", exist_ok=True)

    # 启动应用
    app.run(host='0.0.0.0', port=5000, debug=True)