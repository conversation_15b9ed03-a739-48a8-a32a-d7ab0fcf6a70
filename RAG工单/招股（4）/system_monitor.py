# -*- coding: utf-8 -*-
"""
系统监控和健康检查模块
提供系统状态监控、健康检查和性能指标收集功能
"""

import psutil
import time
import threading
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import json
from dataclasses import dataclass, asdict
from config import Config
from error_handler import log_info, log_warning, log_error, handle_exceptions

@dataclass
class SystemMetrics:
    """
    系统指标数据类
    """
    timestamp: str
    cpu_percent: float
    memory_percent: float
    memory_used_gb: float
    memory_total_gb: float
    disk_percent: float
    disk_used_gb: float
    disk_total_gb: float
    network_sent_mb: float
    network_recv_mb: float
    process_count: int
    thread_count: int

@dataclass
class ComponentStatus:
    """
    组件状态数据类
    """
    name: str
    status: str  # 'healthy', 'warning', 'error', 'unknown'
    last_check: str
    response_time: float
    error_message: Optional[str] = None
    details: Optional[Dict] = None

class SystemMonitor:
    """
    系统监控器
    监控系统资源使用情况和组件健康状态
    """
    
    def __init__(self):
        """
        初始化系统监控器
        """
        self.is_monitoring = False
        self.monitoring_thread = None
        self.metrics_history = []
        self.component_statuses = {}
        self.alerts = []
        
        # 阈值配置
        self.thresholds = {
            'cpu_warning': 80.0,
            'cpu_critical': 95.0,
            'memory_warning': 80.0,
            'memory_critical': 95.0,
            'disk_warning': 85.0,
            'disk_critical': 95.0,
            'response_time_warning': 5.0,
            'response_time_critical': 10.0
        }
        
        # 网络基线（用于计算增量）
        self.network_baseline = None
        
        log_info("系统监控器初始化完成")
    
    @handle_exceptions(fallback_return=None)
    def start_monitoring(self, interval: int = 60):
        """
        启动系统监控
        
        Args:
            interval: 监控间隔（秒）
        """
        if self.is_monitoring:
            log_warning("系统监控已在运行")
            return
        
        self.is_monitoring = True
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            args=(interval,),
            daemon=True
        )
        self.monitoring_thread.start()
        
        log_info(f"系统监控已启动，监控间隔: {interval}秒")
    
    def stop_monitoring(self):
        """
        停止系统监控
        """
        self.is_monitoring = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        
        log_info("系统监控已停止")
    
    def _monitoring_loop(self, interval: int):
        """
        监控循环
        
        Args:
            interval: 监控间隔
        """
        while self.is_monitoring:
            try:
                # 收集系统指标
                metrics = self._collect_system_metrics()
                self._store_metrics(metrics)
                
                # 检查阈值并生成告警
                self._check_thresholds(metrics)
                
                # 清理历史数据（保留最近24小时）
                self._cleanup_old_data()
                
            except Exception as e:
                log_error(e, {"context": "系统监控循环"})
            
            time.sleep(interval)
    
    @handle_exceptions(fallback_return=None)
    def _collect_system_metrics(self) -> SystemMetrics:
        """
        收集系统指标
        
        Returns:
            系统指标对象
        """
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # 内存使用情况
        memory = psutil.virtual_memory()
        memory_used_gb = memory.used / (1024**3)
        memory_total_gb = memory.total / (1024**3)
        
        # 磁盘使用情况
        disk = psutil.disk_usage('/')
        disk_used_gb = disk.used / (1024**3)
        disk_total_gb = disk.total / (1024**3)
        
        # 网络使用情况
        network = psutil.net_io_counters()
        if self.network_baseline is None:
            self.network_baseline = network
            network_sent_mb = 0
            network_recv_mb = 0
        else:
            network_sent_mb = (network.bytes_sent - self.network_baseline.bytes_sent) / (1024**2)
            network_recv_mb = (network.bytes_recv - self.network_baseline.bytes_recv) / (1024**2)
        
        # 进程和线程数量
        process_count = len(psutil.pids())
        thread_count = sum(p.num_threads() for p in psutil.process_iter(['num_threads']) if p.info['num_threads'])
        
        return SystemMetrics(
            timestamp=datetime.now().isoformat(),
            cpu_percent=cpu_percent,
            memory_percent=memory.percent,
            memory_used_gb=round(memory_used_gb, 2),
            memory_total_gb=round(memory_total_gb, 2),
            disk_percent=disk.percent,
            disk_used_gb=round(disk_used_gb, 2),
            disk_total_gb=round(disk_total_gb, 2),
            network_sent_mb=round(network_sent_mb, 2),
            network_recv_mb=round(network_recv_mb, 2),
            process_count=process_count,
            thread_count=thread_count
        )
    
    def _store_metrics(self, metrics: SystemMetrics):
        """
        存储系统指标
        
        Args:
            metrics: 系统指标
        """
        self.metrics_history.append(metrics)
        
        # 限制历史数据数量（最多保留1440个点，即24小时的分钟级数据）
        if len(self.metrics_history) > 1440:
            self.metrics_history = self.metrics_history[-1440:]
    
    def _check_thresholds(self, metrics: SystemMetrics):
        """
        检查阈值并生成告警
        
        Args:
            metrics: 系统指标
        """
        alerts = []
        
        # CPU检查
        if metrics.cpu_percent >= self.thresholds['cpu_critical']:
            alerts.append({
                'level': 'critical',
                'type': 'cpu',
                'message': f"CPU使用率过高: {metrics.cpu_percent}%",
                'timestamp': metrics.timestamp
            })
        elif metrics.cpu_percent >= self.thresholds['cpu_warning']:
            alerts.append({
                'level': 'warning',
                'type': 'cpu',
                'message': f"CPU使用率较高: {metrics.cpu_percent}%",
                'timestamp': metrics.timestamp
            })
        
        # 内存检查
        if metrics.memory_percent >= self.thresholds['memory_critical']:
            alerts.append({
                'level': 'critical',
                'type': 'memory',
                'message': f"内存使用率过高: {metrics.memory_percent}%",
                'timestamp': metrics.timestamp
            })
        elif metrics.memory_percent >= self.thresholds['memory_warning']:
            alerts.append({
                'level': 'warning',
                'type': 'memory',
                'message': f"内存使用率较高: {metrics.memory_percent}%",
                'timestamp': metrics.timestamp
            })
        
        # 磁盘检查
        if metrics.disk_percent >= self.thresholds['disk_critical']:
            alerts.append({
                'level': 'critical',
                'type': 'disk',
                'message': f"磁盘使用率过高: {metrics.disk_percent}%",
                'timestamp': metrics.timestamp
            })
        elif metrics.disk_percent >= self.thresholds['disk_warning']:
            alerts.append({
                'level': 'warning',
                'type': 'disk',
                'message': f"磁盘使用率较高: {metrics.disk_percent}%",
                'timestamp': metrics.timestamp
            })
        
        # 存储告警
        for alert in alerts:
            self.alerts.append(alert)
            if alert['level'] == 'critical':
                log_error(Exception(alert['message']), {"alert": alert})
            else:
                log_warning(alert['message'], {"alert": alert})
        
        # 限制告警历史数量
        if len(self.alerts) > 100:
            self.alerts = self.alerts[-100:]
    
    def _cleanup_old_data(self):
        """
        清理过期数据
        """
        cutoff_time = datetime.now() - timedelta(hours=24)
        cutoff_str = cutoff_time.isoformat()
        
        # 清理指标历史
        self.metrics_history = [
            m for m in self.metrics_history 
            if m.timestamp > cutoff_str
        ]
        
        # 清理告警历史
        self.alerts = [
            a for a in self.alerts 
            if a['timestamp'] > cutoff_str
        ]
    
    @handle_exceptions(fallback_return={})
    def check_component_health(self, component_name: str, 
                             check_function: callable) -> ComponentStatus:
        """
        检查组件健康状态
        
        Args:
            component_name: 组件名称
            check_function: 健康检查函数
            
        Returns:
            组件状态
        """
        start_time = time.time()
        
        try:
            # 执行健康检查
            result = check_function()
            response_time = time.time() - start_time
            
            # 判断状态
            if response_time >= self.thresholds['response_time_critical']:
                status = 'warning'
                error_message = f"响应时间过长: {response_time:.2f}s"
            elif response_time >= self.thresholds['response_time_warning']:
                status = 'warning'
                error_message = f"响应时间较长: {response_time:.2f}s"
            else:
                status = 'healthy'
                error_message = None
            
            component_status = ComponentStatus(
                name=component_name,
                status=status,
                last_check=datetime.now().isoformat(),
                response_time=round(response_time, 3),
                error_message=error_message,
                details=result if isinstance(result, dict) else None
            )
            
        except Exception as e:
            response_time = time.time() - start_time
            component_status = ComponentStatus(
                name=component_name,
                status='error',
                last_check=datetime.now().isoformat(),
                response_time=round(response_time, 3),
                error_message=str(e)
            )
            
            log_error(e, {"component": component_name})
        
        # 存储组件状态
        self.component_statuses[component_name] = component_status
        
        return component_status
    
    def get_system_status(self) -> Dict[str, Any]:
        """
        获取系统状态概览
        
        Returns:
            系统状态字典
        """
        current_metrics = self.metrics_history[-1] if self.metrics_history else None
        
        # 计算平均值（最近1小时）
        recent_metrics = [
            m for m in self.metrics_history 
            if datetime.fromisoformat(m.timestamp) > datetime.now() - timedelta(hours=1)
        ]
        
        avg_cpu = sum(m.cpu_percent for m in recent_metrics) / len(recent_metrics) if recent_metrics else 0
        avg_memory = sum(m.memory_percent for m in recent_metrics) / len(recent_metrics) if recent_metrics else 0
        
        # 统计告警
        recent_alerts = [
            a for a in self.alerts 
            if datetime.fromisoformat(a['timestamp']) > datetime.now() - timedelta(hours=1)
        ]
        
        critical_alerts = len([a for a in recent_alerts if a['level'] == 'critical'])
        warning_alerts = len([a for a in recent_alerts if a['level'] == 'warning'])
        
        return {
            'timestamp': datetime.now().isoformat(),
            'overall_status': self._calculate_overall_status(),
            'current_metrics': asdict(current_metrics) if current_metrics else None,
            'averages': {
                'cpu_percent': round(avg_cpu, 2),
                'memory_percent': round(avg_memory, 2)
            },
            'components': {
                name: asdict(status) 
                for name, status in self.component_statuses.items()
            },
            'alerts': {
                'critical': critical_alerts,
                'warning': warning_alerts,
                'recent': recent_alerts[-10:]  # 最近10个告警
            },
            'monitoring': {
                'is_active': self.is_monitoring,
                'metrics_count': len(self.metrics_history),
                'uptime': self._calculate_uptime()
            }
        }
    
    def _calculate_overall_status(self) -> str:
        """
        计算整体系统状态
        
        Returns:
            状态字符串: 'healthy', 'warning', 'critical'
        """
        # 检查组件状态
        component_statuses = [status.status for status in self.component_statuses.values()]
        
        if 'error' in component_statuses:
            return 'critical'
        elif 'warning' in component_statuses:
            return 'warning'
        
        # 检查最近的告警
        recent_alerts = [
            a for a in self.alerts 
            if datetime.fromisoformat(a['timestamp']) > datetime.now() - timedelta(minutes=10)
        ]
        
        if any(a['level'] == 'critical' for a in recent_alerts):
            return 'critical'
        elif any(a['level'] == 'warning' for a in recent_alerts):
            return 'warning'
        
        return 'healthy'
    
    def _calculate_uptime(self) -> str:
        """
        计算系统运行时间
        
        Returns:
            运行时间字符串
        """
        try:
            boot_time = datetime.fromtimestamp(psutil.boot_time())
            uptime = datetime.now() - boot_time
            
            days = uptime.days
            hours, remainder = divmod(uptime.seconds, 3600)
            minutes, _ = divmod(remainder, 60)
            
            return f"{days}天 {hours}小时 {minutes}分钟"
        except:
            return "未知"
    
    def get_metrics_history(self, hours: int = 1) -> List[Dict]:
        """
        获取指标历史数据
        
        Args:
            hours: 获取最近几小时的数据
            
        Returns:
            指标历史列表
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        cutoff_str = cutoff_time.isoformat()
        
        return [
            asdict(m) for m in self.metrics_history 
            if m.timestamp > cutoff_str
        ]

# 全局系统监控器实例
system_monitor = SystemMonitor()

# 便捷函数
def start_system_monitoring(interval: int = 60):
    """启动系统监控"""
    system_monitor.start_monitoring(interval)

def stop_system_monitoring():
    """停止系统监控"""
    system_monitor.stop_monitoring()

def get_system_status() -> Dict[str, Any]:
    """获取系统状态"""
    return system_monitor.get_system_status()

def check_component_health(component_name: str, check_function: callable) -> ComponentStatus:
    """检查组件健康状态"""
    return system_monitor.check_component_health(component_name, check_function)
