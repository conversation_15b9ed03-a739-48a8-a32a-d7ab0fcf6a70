import logging
import numpy as np
from pymilvus import Collection
from typing import List, Dict, Optional, Tuple, Union
from collections import Counter
import math
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

# 可选导入
try:
    import jieba
    JIEBA_AVAILABLE = True
except ImportError:
    JIEBA_AVAILABLE = False
    logging.warning("jieba未安装，中文分词功能将受限")

class HybridSearchEngine:
    """
    混合检索引擎
    结合向量检索、关键词检索和语义检索
    """

    def __init__(self):
        """
        初始化混合检索引擎
        """
        self.logger = logging.getLogger(__name__)

        # 检索权重配置
        self.vector_weight = 0.6      # 向量检索权重
        self.keyword_weight = 0.3     # 关键词检索权重
        self.semantic_weight = 0.1    # 语义检索权重

        # 性能优化配置
        self.use_parallel = True      # 是否使用并行检索
        self.max_workers = 3          # 最大并行工作线程
        self.cache_size = 1000        # 缓存大小

        # 检索缓存
        self.search_cache = {}

        self.logger.info("混合检索引擎初始化完成")

    def hybrid_search(self, query: str, collection: Collection,
                     embedding_generator, top_k: int = 10,
                     enable_keyword: bool = True,
                     enable_semantic: bool = True) -> List[Dict]:
        """
        混合检索主函数

        Args:
            query: 查询文本
            collection: Milvus集合
            embedding_generator: 嵌入生成器
            top_k: 返回结果数量
            enable_keyword: 是否启用关键词检索
            enable_semantic: 是否启用语义检索

        Returns:
            混合检索结果列表
        """
        try:
            start_time = time.time()

            # 检查缓存
            cache_key = f"{query}_{top_k}_{enable_keyword}_{enable_semantic}"
            if cache_key in self.search_cache:
                self.logger.info("使用缓存结果")
                return self.search_cache[cache_key]

            # 并行执行不同的检索方法
            if self.use_parallel:
                results = self._parallel_search(
                    query, collection, embedding_generator, top_k,
                    enable_keyword, enable_semantic
                )
            else:
                results = self._sequential_search(
                    query, collection, embedding_generator, top_k,
                    enable_keyword, enable_semantic
                )

            # 融合检索结果
            final_results = self._fuse_results(results, top_k)

            # 缓存结果
            if len(self.search_cache) < self.cache_size:
                self.search_cache[cache_key] = final_results

            search_time = time.time() - start_time
            self.logger.info(f"混合检索完成，耗时: {search_time:.3f}秒，返回{len(final_results)}个结果")

            return final_results

        except Exception as e:
            self.logger.error(f"混合检索失败: {str(e)}")
            # 回退到基础向量检索
            return self._fallback_vector_search(query, collection, embedding_generator, top_k)

    def _parallel_search(self, query: str, collection: Collection,
                        embedding_generator, top_k: int,
                        enable_keyword: bool, enable_semantic: bool) -> Dict[str, List]:
        """
        并行执行多种检索方法

        Args:
            query: 查询文本
            collection: Milvus集合
            embedding_generator: 嵌入生成器
            top_k: 返回结果数量
            enable_keyword: 是否启用关键词检索
            enable_semantic: 是否启用语义检索

        Returns:
            各种检索方法的结果字典
        """
        results = {}

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交检索任务
            futures = {}

            # 向量检索
            futures['vector'] = executor.submit(
                self._vector_search, query, collection, embedding_generator, top_k * 2
            )

            # 关键词检索
            if enable_keyword:
                futures['keyword'] = executor.submit(
                    self._keyword_search, query, collection, top_k * 2
                )

            # 语义检索
            if enable_semantic:
                futures['semantic'] = executor.submit(
                    self._semantic_search, query, collection, top_k * 2
                )

            # 收集结果
            for search_type, future in futures.items():
                try:
                    results[search_type] = future.result(timeout=30)
                except Exception as e:
                    self.logger.warning(f"{search_type}检索失败: {str(e)}")
                    results[search_type] = []

        return results

    def _sequential_search(self, query: str, collection: Collection,
                          embedding_generator, top_k: int,
                          enable_keyword: bool, enable_semantic: bool) -> Dict[str, List]:
        """
        顺序执行多种检索方法

        Args:
            query: 查询文本
            collection: Milvus集合
            embedding_generator: 嵌入生成器
            top_k: 返回结果数量
            enable_keyword: 是否启用关键词检索
            enable_semantic: 是否启用语义检索

        Returns:
            各种检索方法的结果字典
        """
        results = {}

        # 向量检索
        results['vector'] = self._vector_search(query, collection, embedding_generator, top_k * 2)

        # 关键词检索
        if enable_keyword:
            results['keyword'] = self._keyword_search(query, collection, top_k * 2)

        # 语义检索
        if enable_semantic:
            results['semantic'] = self._semantic_search(query, collection, top_k * 2)

        return results

    def _vector_search(self, query: str, collection: Collection,
                      embedding_generator, top_k: int) -> List[Dict]:
        """
        向量检索

        Args:
            query: 查询文本
            collection: Milvus集合
            embedding_generator: 嵌入生成器
            top_k: 返回结果数量

        Returns:
            向量检索结果
        """
        try:
            # 生成查询向量
            query_embedding = embedding_generator([query])

            # 确保嵌入是NumPy数组
            if isinstance(query_embedding, list):
                query_embedding = np.array(query_embedding)

            # 检查向量维度
            dim = None
            for field in collection.schema.fields:
                if field.name == "vector":
                    dim = field.dim
                    break

            if dim is None or query_embedding.shape[1] != dim:
                self.logger.error("向量维度不匹配")
                return []

            # 搜索参数优化
            search_params = {
                "metric_type": "L2",
                "params": {"nprobe": min(16, max(1, top_k // 2))}  # 动态调整nprobe
            }

            # 执行搜索
            results = collection.search(
                data=[query_embedding.tolist()[0]],
                anns_field="vector",
                param=search_params,
                limit=top_k,
                output_fields=["text"]
            )

            # 处理结果
            vector_results = []
            for hits in results:
                for hit in hits:
                    vector_results.append({
                        "text": hit.entity.get("text"),
                        "distance": hit.distance,
                        "id": hit.id,
                        "score": 1.0 / (1.0 + hit.distance),  # 转换为相似度分数
                        "method": "vector"
                    })

            return vector_results

        except Exception as e:
            self.logger.error(f"向量检索失败: {str(e)}")
            return []

    def _keyword_search(self, query: str, collection: Collection, top_k: int) -> List[Dict]:
        """
        关键词检索 (基于TF-IDF)

        Args:
            query: 查询文本
            collection: Milvus集合
            top_k: 返回结果数量

        Returns:
            关键词检索结果
        """
        try:
            # 提取查询关键词
            query_keywords = self._extract_keywords(query)
            if not query_keywords:
                return []

            # 获取所有文档进行关键词匹配
            # 注意：这里简化实现，实际应用中可能需要专门的全文检索引擎
            all_results = collection.query(
                expr="id >= 0",  # 获取所有文档
                output_fields=["text"],
                limit=1000  # 限制查询数量
            )

            # 计算TF-IDF分数
            keyword_results = []
            for result in all_results:
                text = result.get("text", "")
                score = self._calculate_tfidf_score(query_keywords, text)

                if score > 0:
                    keyword_results.append({
                        "text": text,
                        "id": result.get("id"),
                        "score": score,
                        "method": "keyword",
                        "matched_keywords": self._get_matched_keywords(query_keywords, text)
                    })

            # 按分数排序
            keyword_results.sort(key=lambda x: x["score"], reverse=True)

            return keyword_results[:top_k]

        except Exception as e:
            self.logger.error(f"关键词检索失败: {str(e)}")
            return []

    def _semantic_search(self, query: str, collection: Collection, top_k: int) -> List[Dict]:
        """
        语义检索 (基于语义相似度)

        Args:
            query: 查询文本
            collection: Milvus集合
            top_k: 返回结果数量

        Returns:
            语义检索结果
        """
        try:
            # 提取查询的语义特征
            query_features = self._extract_semantic_features(query)

            # 获取候选文档
            candidate_results = collection.query(
                expr="id >= 0",
                output_fields=["text"],
                limit=500  # 限制候选数量
            )

            # 计算语义相似度
            semantic_results = []
            for result in candidate_results:
                text = result.get("text", "")
                similarity = self._calculate_semantic_similarity(query_features, text)

                if similarity > 0.1:  # 设置最低相似度阈值
                    semantic_results.append({
                        "text": text,
                        "id": result.get("id"),
                        "score": similarity,
                        "method": "semantic"
                    })

            # 按相似度排序
            semantic_results.sort(key=lambda x: x["score"], reverse=True)

            return semantic_results[:top_k]

        except Exception as e:
            self.logger.error(f"语义检索失败: {str(e)}")
            return []

    def _extract_keywords(self, text: str) -> List[str]:
        """
        提取关键词

        Args:
            text: 输入文本

        Returns:
            关键词列表
        """
        try:
            if JIEBA_AVAILABLE:
                # 使用jieba分词
                words = jieba.cut(text)
            else:
                # 简单的空格分词
                words = text.split()

            # 过滤停用词和短词
            stopwords = {'的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'}
            keywords = [word.strip() for word in words
                       if len(word.strip()) > 1 and word.strip() not in stopwords]

            # 去重并保持顺序
            unique_keywords = []
            seen = set()
            for keyword in keywords:
                if keyword not in seen:
                    unique_keywords.append(keyword)
                    seen.add(keyword)

            return unique_keywords[:10]  # 限制关键词数量

        except Exception as e:
            self.logger.error(f"关键词提取失败: {str(e)}")
            return []

    def _calculate_tfidf_score(self, query_keywords: List[str], text: str) -> float:
        """
        计算TF-IDF分数

        Args:
            query_keywords: 查询关键词列表
            text: 文档文本

        Returns:
            TF-IDF分数
        """
        try:
            # 简化的TF-IDF计算
            text_lower = text.lower()
            score = 0.0

            for keyword in query_keywords:
                keyword_lower = keyword.lower()

                # 计算词频 (TF)
                tf = text_lower.count(keyword_lower)
                if tf > 0:
                    # 简化的IDF计算 (实际应用中需要文档集合统计)
                    idf = math.log(1000 / (tf + 1))  # 假设文档集合大小为1000
                    score += tf * idf

            # 归一化分数
            text_length = len(text.split())
            normalized_score = score / max(text_length, 1)

            return normalized_score

        except Exception as e:
            self.logger.error(f"TF-IDF计算失败: {str(e)}")
            return 0.0

    def _get_matched_keywords(self, query_keywords: List[str], text: str) -> List[str]:
        """
        获取匹配的关键词

        Args:
            query_keywords: 查询关键词列表
            text: 文档文本

        Returns:
            匹配的关键词列表
        """
        text_lower = text.lower()
        matched = []

        for keyword in query_keywords:
            if keyword.lower() in text_lower:
                matched.append(keyword)

        return matched

    def _extract_semantic_features(self, text: str) -> Dict[str, float]:
        """
        提取语义特征

        Args:
            text: 输入文本

        Returns:
            语义特征字典
        """
        try:
            features = {}

            # 词性特征
            if JIEBA_AVAILABLE:
                try:
                    import jieba.posseg as pseg
                    words = pseg.cut(text)
                    pos_counts = Counter()

                    for word, pos in words:
                        if len(word.strip()) > 1:
                            pos_counts[pos] += 1
                except ImportError:
                    pos_counts = Counter()
            else:
                pos_counts = Counter()

            # 归一化词性特征
            total_words = sum(pos_counts.values())
            if total_words > 0:
                for pos, count in pos_counts.items():
                    features[f"pos_{pos}"] = count / total_words

            # 长度特征
            features["text_length"] = len(text)
            features["word_count"] = len(text.split())

            # 数字特征
            import re
            numbers = re.findall(r'\d+', text)
            features["number_count"] = len(numbers)

            return features

        except Exception as e:
            self.logger.error(f"语义特征提取失败: {str(e)}")
            return {}

    def _calculate_semantic_similarity(self, query_features: Dict[str, float], text: str) -> float:
        """
        计算语义相似度

        Args:
            query_features: 查询语义特征
            text: 文档文本

        Returns:
            语义相似度分数
        """
        try:
            # 提取文档语义特征
            doc_features = self._extract_semantic_features(text)

            # 计算特征相似度
            similarity = 0.0
            common_features = set(query_features.keys()) & set(doc_features.keys())

            if common_features:
                for feature in common_features:
                    # 使用余弦相似度
                    query_val = query_features[feature]
                    doc_val = doc_features[feature]

                    if query_val > 0 and doc_val > 0:
                        similarity += min(query_val, doc_val) / max(query_val, doc_val)

                similarity /= len(common_features)

            return similarity

        except Exception as e:
            self.logger.error(f"语义相似度计算失败: {str(e)}")
            return 0.0

    def _fuse_results(self, results: Dict[str, List], top_k: int) -> List[Dict]:
        """
        融合多种检索结果

        Args:
            results: 各种检索方法的结果字典
            top_k: 最终返回的结果数量

        Returns:
            融合后的检索结果
        """
        try:
            # 收集所有结果
            all_results = {}  # id -> result

            # 处理向量检索结果
            if 'vector' in results:
                for result in results['vector']:
                    result_id = result.get('id')
                    if result_id is not None:
                        if result_id not in all_results:
                            all_results[result_id] = result.copy()
                            all_results[result_id]['combined_score'] = 0.0
                            all_results[result_id]['methods'] = []

                        all_results[result_id]['combined_score'] += result['score'] * self.vector_weight
                        all_results[result_id]['methods'].append('vector')

            # 处理关键词检索结果
            if 'keyword' in results:
                for result in results['keyword']:
                    result_id = result.get('id')
                    if result_id is not None:
                        if result_id not in all_results:
                            all_results[result_id] = result.copy()
                            all_results[result_id]['combined_score'] = 0.0
                            all_results[result_id]['methods'] = []

                        all_results[result_id]['combined_score'] += result['score'] * self.keyword_weight
                        all_results[result_id]['methods'].append('keyword')

                        # 保留关键词匹配信息
                        if 'matched_keywords' in result:
                            all_results[result_id]['matched_keywords'] = result['matched_keywords']

            # 处理语义检索结果
            if 'semantic' in results:
                for result in results['semantic']:
                    result_id = result.get('id')
                    if result_id is not None:
                        if result_id not in all_results:
                            all_results[result_id] = result.copy()
                            all_results[result_id]['combined_score'] = 0.0
                            all_results[result_id]['methods'] = []

                        all_results[result_id]['combined_score'] += result['score'] * self.semantic_weight
                        all_results[result_id]['methods'].append('semantic')

            # 转换为列表并排序
            fused_results = list(all_results.values())
            fused_results.sort(key=lambda x: x['combined_score'], reverse=True)

            # 添加融合信息
            for i, result in enumerate(fused_results):
                result['rank'] = i + 1
                result['fusion_method'] = 'hybrid'
                result['method_count'] = len(result['methods'])

            return fused_results[:top_k]

        except Exception as e:
            self.logger.error(f"结果融合失败: {str(e)}")
            # 回退到向量检索结果
            return results.get('vector', [])[:top_k]

    def _fallback_vector_search(self, query: str, collection: Collection,
                               embedding_generator, top_k: int) -> List[Dict]:
        """
        回退到基础向量检索

        Args:
            query: 查询文本
            collection: Milvus集合
            embedding_generator: 嵌入生成器
            top_k: 返回结果数量

        Returns:
            基础向量检索结果
        """
        try:
            return self._vector_search(query, collection, embedding_generator, top_k)
        except Exception as e:
            self.logger.error(f"回退向量检索也失败: {str(e)}")
            return []

    def clear_cache(self):
        """
        清空检索缓存
        """
        self.search_cache.clear()
        self.logger.info("检索缓存已清空")

    def get_cache_stats(self) -> Dict[str, int]:
        """
        获取缓存统计信息

        Returns:
            缓存统计信息
        """
        return {
            "cache_size": len(self.search_cache),
            "max_cache_size": self.cache_size
        }

# 全局混合检索引擎实例
hybrid_search_engine = None

def init_hybrid_search_engine():
    """
    初始化全局混合检索引擎

    Returns:
        HybridSearchEngine实例
    """
    global hybrid_search_engine
    try:
        if hybrid_search_engine is None:
            hybrid_search_engine = HybridSearchEngine()
        return hybrid_search_engine
    except Exception as e:
        logging.error(f"混合检索引擎初始化失败: {str(e)}")
        return None

def get_hybrid_search_engine():
    """
    获取混合检索引擎实例

    Returns:
        HybridSearchEngine实例或None
    """
    global hybrid_search_engine
    if hybrid_search_engine is None:
        return init_hybrid_search_engine()
    return hybrid_search_engine

def search_similar_enhanced(query: str, collection: Collection,
                          embedding_generator, top_k: int = 10,
                          use_hybrid: bool = True) -> List[Dict]:
    """
    增强的相似性搜索

    Args:
        query: 查询文本
        collection: Milvus集合
        embedding_generator: 嵌入生成器
        top_k: 返回结果数量
        use_hybrid: 是否使用混合检索

    Returns:
        搜索结果列表
    """
    try:
        if use_hybrid:
            # 使用混合检索
            try:
                engine = get_hybrid_search_engine()
                if engine:
                    return engine.hybrid_search(query, collection, embedding_generator, top_k)
            except:
                pass  # 混合检索失败，回退到基础检索

        # 回退到原始检索
        return search_similar(query, collection, embedding_generator, top_k)

    except Exception as e:
        logging.error(f"增强搜索失败: {str(e)}")
        return search_similar(query, collection, embedding_generator, top_k)

# 保持向后兼容的原始搜索函数
def search_similar(query, collection, embedding_generator, top_k=10):
    """
    在Milvus中搜索与查询相似的文本

    参数:
        query: 查询文本
        collection: Milvus集合对象
        embedding_generator: 嵌入生成函数
        top_k: 返回的结果数量

    返回:
        相似文本列表，每个元素包含文本内容、距离和ID
    """
    try:
        # 生成查询向量
        query_embedding = embedding_generator([query])

        # 确保嵌入是NumPy数组
        if isinstance(query_embedding, list):
            query_embedding = np.array(query_embedding)

        # 检查向量维度是否匹配（使用正确的字段访问方式）
        dim = None
        for field in collection.schema.fields:
            if field.name == "vector":
                dim = field.dim
                break

        if dim is None:
            logging.error("在集合中找不到向量字段")
            return []

        if query_embedding.shape[1] != dim:
            logging.error(f"向量维度不匹配! 查询向量维度: {query_embedding.shape[1]}, 集合维度: {dim}")
            return []

        # 搜索参数
        search_params = {
            "metric_type": "L2",
            "params": {"nprobe": 10}
        }

        # 执行搜索
        results = collection.search(
            data=[query_embedding.tolist()[0]],  # 确保是列表的列表
            anns_field="vector",
            param=search_params,
            limit=top_k,
            output_fields=["text"]
        )

        # 处理结果（添加完整的返回信息）
        similar_texts = []
        for hits in results:
            for hit in hits:
                similar_texts.append({
                    "text": hit.entity.get("text"),
                    "distance": hit.distance,  # 添加距离信息
                    "id": hit.id  # 添加ID信息
                })

        return similar_texts

    except Exception as e:
        logging.error(f"向量搜索失败: {str(e)}", exc_info=True)
        return []