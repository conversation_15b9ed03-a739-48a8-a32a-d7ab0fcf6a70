# 智能文档问答系统 v2.0

一个功能完整、高性能的RAG（检索增强生成）文档问答系统，支持多种文件格式、语音交互、智能分类和混合检索。

## 🌟 主要特性

### 核心功能
- **多轮对话**: 支持上下文记忆的连续对话，聊天记录保存在Redis中
- **多文件格式支持**: PDF、Word、Excel、图片、音频等多种格式
- **智能文档处理**: 自动识别表格、图片，保持文档结构完整性
- **混合检索**: 结合向量检索、关键词检索和语义检索
- **智能分割**: 保持语义连贯性和上下文联系的文档分割

### 增强功能
- **语音交互**: 语音转文字输入，文字转语音输出
- **智能分类**: 命名实体识别和问题自动分类
- **可视化界面**: 直观的参考文档展示和相关度可视化
- **系统监控**: 实时系统状态监控和性能指标
- **错误处理**: 完善的错误处理和日志记录

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   Flask API     │    │   向量数据库    │
│   (HTML/JS)     │◄──►│   (Python)      │◄──►│   (Milvus)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   语音处理      │    │   文档处理      │    │   LLM服务       │
│   (ASR/TTS)     │    │   (多格式)      │    │   (Qwen)        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Redis缓存     │    │   系统监控      │    │   错误处理      │
│   (会话存储)    │    │   (性能指标)    │    │   (日志记录)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 快速开始

### 环境要求
- Python 3.8+
- Redis 服务器
- Milvus 向量数据库
- 足够的存储空间用于模型文件

### 1. 克隆项目
```bash
git clone <repository-url>
cd RAG工单/招股（4）
```

### 2. 安装依赖
```bash
# 自动安装所有依赖
python run.py --install-deps

# 或手动安装
pip install -r requirements.txt
```

### 3. 配置模型路径
编辑 `config.py` 文件，设置正确的模型路径：
```python
EMBEDDING_MODEL_PATH = "你的BGE-M3模型路径"
RERANKER_MODEL_PATH = "你的BGE-Reranker模型路径"
```

### 4. 启动服务
```bash
# 开发模式
python run.py --mode development

# 生产模式
python run.py --mode production

# 检查环境
python run.py --check-only
```

### 5. 访问系统
打开浏览器访问: http://localhost:5000

## 📁 项目结构

```
RAG工单/招股（4）/
├── app.py                 # 主应用文件
├── config.py              # 配置管理
├── run.py                 # 启动脚本
├── requirements.txt       # 依赖列表
├── README.md             # 项目文档
│
├── 核心模块/
│   ├── embeddings.py     # 嵌入模型
│   ├── llm_client.py     # LLM客户端
│   ├── milvus_manager.py # 向量数据库管理
│   ├── redis_manager.py  # Redis管理
│   └── reranker.py       # 重排序模块
│
├── 增强功能/
│   ├── file_processor.py # 增强文件处理
│   ├── vector_search.py  # 混合检索
│   ├── speech_processor.py # 语音处理
│   └── ner_classifier.py # 智能分类
│
├── 系统模块/
│   ├── error_handler.py  # 错误处理
│   └── system_monitor.py # 系统监控
│
├── 前端/
│   └── templates/
│       └── index.html    # 主界面
│
└── 数据目录/
    ├── uploads/          # 上传文件
    ├── static/audio/     # 语音文件
    └── logs/            # 日志文件
```

## 🔧 配置说明

### 主要配置项
- **数据库配置**: Milvus和Redis连接参数
- **模型配置**: 嵌入模型、重排序模型、LLM配置
- **文件处理**: 支持的文件类型、大小限制
- **检索配置**: 混合检索权重、缓存设置
- **语音配置**: ASR/TTS参数设置

### 环境变量
```bash
export FLASK_ENV=development  # 运行环境
export SECRET_KEY=your-secret-key  # 会话密钥
```

## 📊 功能详解

### 1. 多轮对话
- 基于Redis的会话管理
- 上下文记忆和连续对话
- 会话历史查看和清理

### 2. 文件处理
- **文档类型**: PDF、Word、TXT
- **图片类型**: JPG、PNG、TIFF等，支持OCR
- **表格类型**: Excel、CSV，智能表格识别
- **音频类型**: WAV、MP3等，语音转文字

### 3. 智能检索
- **向量检索**: 基于语义相似度
- **关键词检索**: 基于TF-IDF算法
- **语义检索**: 基于语义特征匹配
- **混合检索**: 多种方法融合排序

### 4. 语音交互
- **语音输入**: 支持多种音频格式
- **语音输出**: 高质量TTS合成
- **实时处理**: 流式语音识别

### 5. 智能分类
- **实体识别**: 金额、时间、公司等实体
- **问题分类**: 财务、风险、业务等类别
- **智能路由**: 根据分类生成专业提示词

## 🔍 API接口

### 文件上传
```http
POST /upload
Content-Type: multipart/form-data

{
  "file": <文件数据>
}
```

### 智能查询
```http
POST /query
Content-Type: application/json

{
  "query": "用户问题",
  "use_hybrid_search": true,
  "use_ner_classification": true,
  "enable_tts": false
}
```

### 语音处理
```http
POST /speech-to-text
Content-Type: multipart/form-data

POST /text-to-speech
Content-Type: application/json
```

### 系统状态
```http
GET /system-status
```

## 📈 性能优化

### 1. 检索优化
- 智能缓存机制
- 并行检索处理
- 结果去重和排序

### 2. 文档处理优化
- 多线程处理
- 智能分割算法
- 内存管理优化

### 3. 系统监控
- 实时性能指标
- 错误统计分析
- 资源使用监控

## 🛠️ 开发指南

### 添加新的文件类型支持
1. 在 `file_processor.py` 中添加处理函数
2. 更新 `config.py` 中的 `ALLOWED_EXTENSIONS`
3. 在前端添加相应的UI支持

### 添加新的检索方法
1. 在 `vector_search.py` 中实现检索函数
2. 更新 `HybridSearchEngine` 类
3. 调整权重配置

### 自定义问题分类
1. 修改 `ner_classifier.py` 中的分类规则
2. 添加新的提示词模板
3. 更新前端分类显示

## 🐛 故障排除

### 常见问题
1. **模型加载失败**: 检查模型路径和文件完整性
2. **数据库连接失败**: 确认Milvus和Redis服务状态
3. **语音功能异常**: 检查音频设备和权限设置
4. **文件上传失败**: 检查文件大小和格式限制

### 日志查看
```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log
```

## 📝 更新日志

### v2.0.0
- ✨ 新增语音交互功能
- ✨ 新增智能分类和NER
- ✨ 新增混合检索引擎
- ✨ 新增系统监控和错误处理
- 🔧 优化文档处理和分割算法
- 🎨 全新的前端界面设计
- 📈 性能优化和缓存机制

### v1.0.0
- 🎉 基础RAG功能实现
- 📄 多格式文档支持
- 🔍 向量检索功能
- 💬 基础对话功能

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者

---

**享受智能文档问答的便利！** 🚀
