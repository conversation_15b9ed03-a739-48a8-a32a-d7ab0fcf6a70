# Web框架
Flask==2.3.3
Flask-CORS==4.0.0

# 向量数据库
pymilvus==2.3.4

# 机器学习和深度学习
torch==2.1.0
transformers==4.35.0
sentence-transformers==2.2.2
FlagEmbedding==1.2.5

# 文档处理
PyMuPDF==1.23.8
python-docx==0.8.11
openpyxl==3.1.2
pandas==2.1.3
camelot-py[cv]==0.11.0
tabula-py==2.8.2

# 图像处理和OCR
Pillow==10.1.0
pytesseract==0.3.10
opencv-python==4.8.1.78

# 音频处理
pydub==0.25.1
SpeechRecognition==3.10.0
pyttsx3==2.90

# 自然语言处理
jieba==0.42.1
numpy==1.24.3

# 数据库和缓存
redis==5.0.1

# HTTP客户端
openai==1.3.5
requests==2.31.0

# 系统监控
psutil==5.9.6

# 工具库
python-dotenv==1.0.0
tqdm==4.66.1
colorama==0.4.6

# 开发和测试
pytest==7.4.3
pytest-cov==4.1.0
black==23.11.0
flake8==6.1.0

# 可选依赖（根据需要安装）
# GPU支持（如果有NVIDIA GPU）
# torch==2.1.0+cu118 -f https://download.pytorch.org/whl/torch_stable.html

# 额外的语音识别引擎
# google-cloud-speech==2.21.0
# azure-cognitiveservices-speech==1.34.0

# 额外的文档处理
# python-pptx==0.6.22
# python-magic==0.4.27

# 性能优化
# numba==0.58.1
# faiss-cpu==1.7.4  # 或 faiss-gpu 如果有GPU

# 日志和监控
# prometheus-client==0.19.0
# structlog==23.2.0
