#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的启动脚本
确保应用能够正常启动，即使某些模块缺失
"""

import os
import sys
import logging

# 设置基本日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_basic_requirements():
    """
    检查基本依赖
    """
    required_modules = [
        'flask',
        'pymilvus', 
        'redis',
        'fitz',  # PyMuPDF
        'PIL',   # Pillow
    ]
    
    missing = []
    for module in required_modules:
        try:
            if module == 'fitz':
                import fitz
            elif module == 'PIL':
                from PIL import Image
            else:
                __import__(module)
            logger.info(f"✓ {module} 已安装")
        except ImportError:
            missing.append(module)
            logger.error(f"✗ {module} 未安装")
    
    if missing:
        logger.error(f"缺少必需模块: {missing}")
        logger.info("请运行: pip install flask pymilvus redis PyMuPDF Pillow")
        return False
    
    return True

def setup_directories():
    """
    创建必要的目录
    """
    directories = [
        'uploads',
        'static',
        'static/audio',
        'logs'
    ]
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            logger.info(f"✓ 目录创建: {directory}")
        except Exception as e:
            logger.warning(f"目录创建失败 {directory}: {e}")

def main():
    """
    主启动函数
    """
    logger.info("=" * 50)
    logger.info("智能文档问答系统启动")
    logger.info("=" * 50)
    
    # 检查基本依赖
    if not check_basic_requirements():
        logger.error("基本依赖检查失败，无法启动")
        sys.exit(1)
    
    # 创建目录
    setup_directories()
    
    # 导入并启动应用
    try:
        logger.info("正在导入应用模块...")
        from app import create_app
        
        logger.info("正在创建应用实例...")
        app = create_app()
        
        if app is None:
            logger.error("应用创建失败")
            sys.exit(1)
        
        logger.info("应用创建成功，正在启动服务器...")
        logger.info("访问地址: http://localhost:5000")
        logger.info("按 Ctrl+C 停止服务器")
        
        # 启动应用
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=True,
            threaded=True
        )
        
    except KeyboardInterrupt:
        logger.info("收到停止信号，正在关闭...")
    except Exception as e:
        logger.error(f"应用启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
