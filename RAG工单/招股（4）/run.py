#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用启动脚本
提供多种启动模式和配置选项
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path

def check_dependencies():
    """
    检查依赖是否安装
    
    Returns:
        bool: 依赖是否完整
    """
    required_packages = [
        'flask', 'pymilvus', 'torch', 'transformers', 
        'redis', 'jieba', 'PIL', 'cv2'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'PIL':
                import PIL
            elif package == 'cv2':
                import cv2
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def check_models():
    """
    检查模型文件是否存在
    
    Returns:
        bool: 模型是否完整
    """
    from config import Config
    
    model_paths = [
        Config.EMBEDDING_MODEL_PATH,
        Config.RERANKER_MODEL_PATH
    ]
    
    missing_models = []
    
    for model_path in model_paths:
        if not os.path.exists(model_path):
            missing_models.append(model_path)
    
    if missing_models:
        print(f"❌ 缺少模型文件:")
        for model in missing_models:
            print(f"   - {model}")
        print("请下载相应的模型文件")
        return False
    
    print("✅ 所有模型文件已就绪")
    return True

def check_services():
    """
    检查外部服务是否可用
    
    Returns:
        bool: 服务是否可用
    """
    from config import Config
    import socket
    
    services = [
        ("Milvus", Config.MILVUS_HOST, int(Config.MILVUS_PORT)),
        ("Redis", Config.REDIS_HOST, Config.REDIS_PORT)
    ]
    
    unavailable_services = []
    
    for service_name, host, port in services:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result != 0:
                unavailable_services.append(f"{service_name} ({host}:{port})")
        except Exception:
            unavailable_services.append(f"{service_name} ({host}:{port})")
    
    if unavailable_services:
        print(f"❌ 无法连接到服务:")
        for service in unavailable_services:
            print(f"   - {service}")
        print("请确保相关服务已启动")
        return False
    
    print("✅ 所有外部服务可用")
    return True

def setup_environment():
    """
    设置环境变量和目录
    """
    # 创建必要的目录
    directories = [
        "uploads",
        "static/audio",
        "logs",
        "cache"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"📁 创建目录: {directory}")
    
    # 设置环境变量
    if not os.environ.get('FLASK_ENV'):
        os.environ['FLASK_ENV'] = 'development'
    
    print("🔧 环境设置完成")

def install_dependencies():
    """
    安装依赖包
    """
    print("📦 正在安装依赖包...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ 依赖包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        return False

def run_tests():
    """
    运行测试
    """
    print("🧪 运行测试...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pytest", "tests/", "-v"])
        print("✅ 所有测试通过")
        return True
    except subprocess.CalledProcessError:
        print("❌ 测试失败")
        return False
    except FileNotFoundError:
        print("⚠️  未找到测试文件，跳过测试")
        return True

def start_application(mode="development", host="0.0.0.0", port=5000):
    """
    启动应用
    
    Args:
        mode: 运行模式 (development, production, testing)
        host: 主机地址
        port: 端口号
    """
    print(f"🚀 启动应用 - 模式: {mode}")
    
    # 设置环境变量
    os.environ['FLASK_ENV'] = mode
    
    if mode == "production":
        # 生产模式使用gunicorn
        try:
            subprocess.check_call([
                "gunicorn",
                "--bind", f"{host}:{port}",
                "--workers", "4",
                "--worker-class", "sync",
                "--timeout", "120",
                "--keep-alive", "5",
                "--max-requests", "1000",
                "--max-requests-jitter", "100",
                "--access-logfile", "logs/access.log",
                "--error-logfile", "logs/error.log",
                "app:app"
            ])
        except FileNotFoundError:
            print("❌ 未安装gunicorn，使用开发模式启动")
            from app import create_app
            app = create_app()
            if app:
                app.run(host=host, port=port, debug=False)
    else:
        # 开发模式
        from app import create_app
        app = create_app()
        if app:
            app.run(host=host, port=port, debug=(mode == "development"))

def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description="智能文档问答系统启动脚本")
    
    parser.add_argument(
        "--mode", 
        choices=["development", "production", "testing"],
        default="development",
        help="运行模式"
    )
    
    parser.add_argument(
        "--host",
        default="0.0.0.0",
        help="主机地址"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=5000,
        help="端口号"
    )
    
    parser.add_argument(
        "--install-deps",
        action="store_true",
        help="安装依赖包"
    )
    
    parser.add_argument(
        "--check-only",
        action="store_true",
        help="仅检查环境，不启动应用"
    )
    
    parser.add_argument(
        "--run-tests",
        action="store_true",
        help="运行测试"
    )
    
    parser.add_argument(
        "--skip-checks",
        action="store_true",
        help="跳过环境检查"
    )
    
    args = parser.parse_args()
    
    print("🔍 智能文档问答系统启动检查")
    print("=" * 50)
    
    # 安装依赖
    if args.install_deps:
        if not install_dependencies():
            sys.exit(1)
    
    # 环境检查
    if not args.skip_checks:
        setup_environment()
        
        if not check_dependencies():
            print("\n💡 提示: 使用 --install-deps 自动安装依赖")
            sys.exit(1)
        
        if not check_models():
            sys.exit(1)
        
        if not check_services():
            sys.exit(1)
    
    # 运行测试
    if args.run_tests:
        if not run_tests():
            sys.exit(1)
    
    # 仅检查模式
    if args.check_only:
        print("\n✅ 环境检查完成，系统就绪")
        return
    
    print("\n" + "=" * 50)
    print("🎉 环境检查通过，启动应用...")
    
    try:
        start_application(args.mode, args.host, args.port)
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"\n❌ 应用启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
