import openai
import logging

# 全局LLM客户端
llm_client = None


def init_llm_client():
    global llm_client
    try:
        llm_client = openai.OpenAI(
            api_key="ms-6f7de516-7098-44af-969d-b1d28d65ec00",
            base_url="https://api-inference.modelscope.cn/v1/",
            timeout=30
        )
        return llm_client
    except Exception as e:
        logging.error(f"LLM client initialization error: {str(e)}")
        raise


def generate_answer(query, context, client, chat_history=[]):
    try:
        # 构建系统消息
        messages = [
            {
                "role": "system",
                "content": "你是一个AI助手，基于上传的文档内容回答用户问题。保持回答简洁专业，使用Markdown格式组织内容。"
            }
        ]

        # 添加历史对话
        messages.extend(chat_history)

        # 添加上下文
        context_text = "\n\n".join([item["text"] for item in context])
        if len(context_text) > 3000:
            context_text = context_text[:3000] + "...[内容已截断]"

        messages.append({
            "role": "system",
            "content": f"文档上下文信息：\n{context_text}"
        })

        # 添加当前查询
        messages.append({
            "role": "user",
            "content": query
        })

        response = client.chat.completions.create(
            model="Qwen/Qwen2.5-Coder-32B-Instruct",
            messages=messages,
            stream=False,
            timeout=20
        )

        return response.choices[0].message.content.strip()

    except openai.RateLimitError:
        logging.error("API请求达到速率限制")
        return "抱歉，当前请求过于频繁，请稍后再试。"
    except Exception as e:
        logging.error(f"生成答案错误: {str(e)}")
        return "生成答案时出现错误，请稍后再试。"


def generate_answer_with_prompt(enhanced_prompt: str, client, chat_history=[]):
    """
    使用增强提示词生成答案

    Args:
        enhanced_prompt: 增强的提示词
        client: LLM客户端
        chat_history: 聊天历史

    Returns:
        生成的答案
    """
    try:
        # 构建消息
        messages = [
            {
                "role": "system",
                "content": "你是一个专业的AI助手，请根据提供的专业提示词和上下文信息回答用户问题。"
            }
        ]

        # 添加历史对话（限制长度）
        if chat_history:
            # 只保留最近的对话
            recent_history = chat_history[-6:] if len(chat_history) > 6 else chat_history
            messages.extend(recent_history)

        # 添加增强提示词
        messages.append({
            "role": "user",
            "content": enhanced_prompt
        })

        # 调用LLM
        response = client.chat.completions.create(
            model="Qwen/Qwen2.5-Coder-32B-Instruct",
            messages=messages,
            stream=False,
            timeout=30,
            temperature=0.7,  # 稍微增加创造性
            max_tokens=2000   # 限制回答长度
        )

        return response.choices[0].message.content.strip()

    except openai.RateLimitError:
        logging.error("API请求达到速率限制")
        return "抱歉，当前请求过于频繁，请稍后再试。"
    except Exception as e:
        logging.error(f"增强提示词生成答案错误: {str(e)}")
        return "生成答案时出现错误，请稍后再试。"


def generate_streaming_answer(query: str, context: list, client, chat_history=[]):
    """
    生成流式答案（用于实时响应）

    Args:
        query: 用户查询
        context: 上下文文档
        client: LLM客户端
        chat_history: 聊天历史

    Yields:
        答案片段
    """
    try:
        # 构建系统消息
        messages = [
            {
                "role": "system",
                "content": "你是一个AI助手，基于上传的文档内容回答用户问题。保持回答简洁专业，使用Markdown格式组织内容。"
            }
        ]

        # 添加历史对话
        messages.extend(chat_history[-4:] if len(chat_history) > 4 else chat_history)

        # 添加上下文
        context_text = "\n\n".join([item.get("text", "") for item in context])
        if len(context_text) > 3000:
            context_text = context_text[:3000] + "...[内容已截断]"

        messages.append({
            "role": "system",
            "content": f"文档上下文信息：\n{context_text}"
        })

        # 添加当前查询
        messages.append({
            "role": "user",
            "content": query
        })

        # 流式调用
        response = client.chat.completions.create(
            model="Qwen/Qwen2.5-Coder-32B-Instruct",
            messages=messages,
            stream=True,
            timeout=30
        )

        for chunk in response:
            if chunk.choices[0].delta.content:
                yield chunk.choices[0].delta.content

    except Exception as e:
        logging.error(f"流式生成答案错误: {str(e)}")
        yield f"生成答案时出现错误: {str(e)}"


def validate_llm_response(response: str) -> dict:
    """
    验证LLM响应的质量

    Args:
        response: LLM响应文本

    Returns:
        验证结果字典
    """
    try:
        validation = {
            "is_valid": True,
            "length": len(response),
            "word_count": len(response.split()),
            "has_markdown": "**" in response or "*" in response or "#" in response,
            "has_structure": "\n" in response,
            "quality_score": 0.0,
            "issues": []
        }

        # 长度检查
        if validation["length"] < 10:
            validation["is_valid"] = False
            validation["issues"].append("回答过短")
        elif validation["length"] > 5000:
            validation["issues"].append("回答过长")

        # 内容质量检查
        if "错误" in response or "失败" in response:
            validation["issues"].append("包含错误信息")

        if response.count("。") < 2:
            validation["issues"].append("缺少句子结构")

        # 计算质量分数
        score = 0.5  # 基础分数

        if validation["word_count"] > 20:
            score += 0.2
        if validation["has_markdown"]:
            score += 0.1
        if validation["has_structure"]:
            score += 0.1
        if len(validation["issues"]) == 0:
            score += 0.1

        validation["quality_score"] = min(score, 1.0)

        return validation

    except Exception as e:
        logging.error(f"响应验证失败: {str(e)}")
        return {
            "is_valid": False,
            "quality_score": 0.0,
            "issues": ["验证过程出错"]
        }