app.py - 主应用入口
功能
创建Flask应用并配置CORS
初始化全局组件（Milvus、嵌入模型、LLM客户端）
处理文件上传和用户查询请求
协调整个RAG流程
关键技术
Flask框架：

创建Web服务
路由处理（@app.route）
请求处理（request对象）
响应生成（jsonify, render_template）
应用初始化：

全局组件初始化（Milvus、嵌入模型、LLM）
错误处理和日志记录
RAG流程控制：

文件上传处理 → 向量搜索 → 重排序 → 答案生成
协调各模块协同工作
配置管理：

上传文件夹设置
允许的文件类型配置
注意事项
确保Milvus服务已启动
嵌入模型和重排序模型路径正确
文件上传目录有写入权限
embeddings.py - 嵌入模型处理
功能
加载本地嵌入模型（BGE-M3）
为文本生成嵌入向量
关键技术
Sentence Transformers库：

加载本地预训练模型
使用encode()方法生成嵌入
模型管理：

全局模型实例（避免重复加载）
按需初始化模型
错误处理：

模型加载失败处理
嵌入生成异常捕获
注意事项
模型路径需指向有效的本地模型目录
首次加载模型可能较慢
确保有足够内存加载模型
file_processor.py - 文件处理
功能
验证文件类型
解析PDF/TXT文件内容
文本分块处理
协调文件处理全流程
关键技术
文件处理：

文件类型验证（allowed_file）
文件保存和删除
文本提取：

PyMuPDF（fitz）处理PDF
正则表达式清理文本（re.sub）
文本分块：

固定大小分块（500字符）
重叠分块策略（50字符重叠）
流程协调：

调用嵌入生成
调用向量存储
注意事项
安装PyMuPDF库（pip install pymupdf）
处理大文件时注意内存使用
分块参数（大小和重叠）可根据需求调整
4. llm_client.py - 大语言模型客户端
功能
初始化OpenAI兼容客户端
使用上下文生成回答
关键技术
OpenAI SDK：

配置API密钥和基础URL
创建客户端实例
提示工程：

系统角色设置
上下文和问题拼接
结构化消息传递
模型调用：

使用Qwen2.5-Coder-32B-Instruct模型
同步调用（非流式）
错误处理：

客户端初始化错误
回答生成异常捕获
注意事项
替换为有效的ModelScope SDK Token
确保网络可访问ModelScope API
模型名称需与API提供的一致
milvus_manager.py - Milvus向量数据库管理
功能
连接Milvus数据库
创建/加载集合
存储文本和向量
关键技术
PyMilvus SDK：

数据库连接管理
集合操作（创建/加载）
模式定义：

主键字段（自增ID）
文本字段（VARCHAR）
向量字段（1024维浮点向量）
索引管理：

IVF_FLAT索引类型
L2距离度量
nlist参数配置
数据操作：

批量插入数据
刷新确保数据持久化
注意事项
确保Milvus服务运行在localhost:19530
集合名称需唯一
向量维度需与嵌入模型输出一致（1024）

reranker.py - 重排序模块
功能
加载本地重排序模型
对搜索结果进行相关性重排序
关键技术
Sentence Transformers：

使用CrossEncoder模型
本地模型加载（BGE-Reranker-v2-M3）
重排序算法：

查询-文档对评分
相关性分数计算
结果处理：

按分数降序排序
添加重排序元数据
错误处理：

模型加载失败回退
重排序异常处理
注意事项
模型路径需指向有效的本地模型目录
首次加载模型可能较慢（30-60秒）
确保有足够内存加载模型

vector_search.py - 向量搜索
功能
生成查询向量
在Milvus中执行相似度搜索
关键技术
向量搜索：

查询向量生成
Milvus ANN搜索
搜索参数：

返回结果数量（top_k）
nprobe参数（搜索范围）
结果处理：

提取文本和距离
结果格式化
类型处理：

NumPy数组转换
嵌入向量格式统一
注意事项
确保集合已加载
搜索参数（nprobe）影响性能和召回率
处理空结果情况
整体技术栈
技术领域	使用技术/库
Web框架	Flask + Flask-CORS
向量数据库	Milvus + PyMilvus
嵌入模型	Sentence Transformers
文本处理	PyMuPDF, 正则表达式
大语言模型	OpenAI SDK + ModelScope
重排序	CrossEncoder
数据处理	NumPy
日志管理	logging
部署要求
Python 3.8+
安装依赖：pip install -r requirements.txt
运行Milvus服务（单机版）
下载嵌入模型和重排序模型到本地路径
启动步骤
启动Milvus：docker-compose up -d（使用官方配置）
运行应用：python app.py
访问：http://localhost:5000
性能优化建议
使用GPU加速嵌入模型和重排序模型
增加Milvus索引参数（nlist）提升搜索效率
实现异步处理大文件上传
添加缓存机制减少重复计算
安全注意事项
保护ModelScope API密钥
验证上传文件类型防止恶意文件
限制上传文件大小
添加API访问认证