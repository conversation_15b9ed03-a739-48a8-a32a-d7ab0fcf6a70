import os
import logging
from typing import List, Dict, Optional
import numpy as np
from sentence_transformers import CrossEncoder

# 本地重排序模型路径
LOCAL_RERANK_MODEL_PATH = r"D:\model\BAAI\bge-reranker-v2-m3"  # 替换为您的本地模型路径

# 全局模型实例
rerank_model = None


def rerank_documents(
        query: str,
        documents: List[Dict],
        top_n: int = 3,
        model_name: Optional[str] = None
) -> List[Dict]:
    """
    使用本地模型对文档进行重排序

    参数:
        query: 原始查询文本
        documents: 文档列表，每个文档包含"text"和"distance"
        top_n: 返回的顶部文档数量
        model_name: 可选的重排序模型名称（未使用，保留参数）

    返回:
        重排序后的文档列表
    """
    global rerank_model

    if not documents:
        return []

    # 加载模型（如果未加载）
    if rerank_model is None:
        try:
            logging.info("加载本地重排序模型...")
            rerank_model = CrossEncoder(LOCAL_RERANK_MODEL_PATH)
            logging.info("本地重排序模型加载成功")
        except Exception as e:
            logging.error(f"加载本地重排序模型失败: {str(e)}")
            return documents[:top_n]

    try:
        # 准备查询-文档对
        pairs = [(query, doc["text"]) for doc in documents]

        # 计算相关性分数
        scores = rerank_model.predict(pairs)

        # 合并分数到文档
        for i, doc in enumerate(documents):
            doc["rerank_score"] = float(scores[i])
            doc["rerank_model"] = "local-bge-reranker-v2-m3"

        # 按重排序分数降序排序
        reranked_docs = sorted(documents, key=lambda x: x["rerank_score"], reverse=True)

        return reranked_docs[:top_n]

    except Exception as e:
        logging.error(f"本地重排序失败: {str(e)}")
        # 失败时返回原始top_n结果
        return documents[:top_n]