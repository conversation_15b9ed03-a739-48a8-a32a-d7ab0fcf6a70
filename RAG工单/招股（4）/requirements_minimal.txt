# 最小化依赖列表 - 确保基本功能运行

# Web框架
Flask==2.3.3
Flask-CORS==4.0.0

# 向量数据库
pymilvus==2.3.4

# 机器学习和深度学习
torch==2.1.0
transformers==4.35.0
sentence-transformers==2.2.2

# 文档处理
PyMuPDF==1.23.8
pandas==2.1.3

# 图像处理
Pillow==10.1.0

# 数据库和缓存
redis==5.0.1

# HTTP客户端
openai==1.3.5
requests==2.31.0

# 工具库
numpy==1.24.3
tqdm==4.66.1

# 可选依赖（如果需要完整功能）
# jieba==0.42.1
# pytesseract==0.3.10
# opencv-python==4.8.1.78
# python-docx==0.8.11
# openpyxl==3.1.2
# pydub==0.25.1
# SpeechRecognition==3.10.0
# pyttsx3==2.90
# psutil==5.9.6
