import os
import re
import fitz  # PyMuPDF for PDF processing
import logging
from PIL import Image
import pytesseract


def allowed_file(filename, allowed_extensions):
    return '.' in filename and \
        filename.rsplit('.', 1)[1].lower() in allowed_extensions


def process_pdf(file_path):
    try:
        doc = fitz.open(file_path)
        text = ""
        for page in doc:
            # 先尝试直接提取文本
            page_text = page.get_text("text")

            # 如果提取的文本过少（可能是图片PDF），尝试使用OCR
            if len(page_text) < 100:
                try:
                    pix = page.get_pixmap()
                    img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
                    page_text = pytesseract.image_to_string(img, lang='chi_sim')
                except Exception as ocr_error:
                    logging.warning(f"OCR失败: {str(ocr_error)}")

            text += page_text

        # 清理文本
        text = re.sub(r'\s+', ' ', text).strip()
        return text
    except Exception as e:
        raise Exception(f"处理PDF错误: {str(e)}")


def process_txt(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            text = f.read()
        # 清理文本
        text = re.sub(r'\s+', ' ', text).strip()
        return text
    except Exception as e:
        raise Exception(f"处理TXT错误: {str(e)}")


def chunk_text(text, min_chunk_size=200, max_chunk_size=500, overlap=50):
    """
    按段落切分文本并加入重叠

    参数:
        text: 原始文本
        min_chunk_size: 最小分块大小(字符数)
        max_chunk_size: 最大分块大小(字符数)
        overlap: 重叠区域大小(字符数)

    返回:
        分块后的文本列表
    """
    # 按段落分割（考虑多种换行符）
    paragraphs = [p.strip() for p in re.split(r'\n\s*\n', text) if p.strip()]

    chunks = []
    current_chunk = ""

    for para in paragraphs:
        # 如果当前段落很短，直接添加到当前块
        if len(current_chunk) + len(para) < min_chunk_size:
            if current_chunk:
                current_chunk += "\n\n"
            current_chunk += para
            continue

        # 如果当前段落能直接加入当前块
        if len(current_chunk) + len(para) <= max_chunk_size:
            if current_chunk:
                current_chunk += "\n\n"
            current_chunk += para
        else:
            # 处理长段落
            start_idx = 0
            while start_idx < len(para):
                # 计算可以添加到当前块的文本量
                remaining_space = max_chunk_size - len(current_chunk) - 2  # 减去换行符

                if remaining_space > 0:
                    # 添加部分段落到当前块
                    end_idx = min(start_idx + remaining_space, len(para))
                    if current_chunk:
                        current_chunk += "\n\n"
                    current_chunk += para[start_idx:end_idx]
                    start_idx = end_idx

                # 如果当前块达到最大大小，保存它
                if len(current_chunk) >= min_chunk_size:
                    chunks.append(current_chunk)

                    # 创建重叠的新块
                    if overlap > 0 and chunks:
                        last_chunk = chunks[-1]
                        overlap_start = max(0, len(last_chunk) - overlap)
                        current_chunk = last_chunk[overlap_start:]
                    else:
                        current_chunk = ""

                # 如果剩余段落长度超过最大块大小，直接分割
                if len(para) - start_idx > max_chunk_size:
                    end_idx = min(start_idx + max_chunk_size, len(para))
                    chunks.append(para[start_idx:end_idx])
                    start_idx = end_idx

    # 添加最后一个块
    if current_chunk:
        chunks.append(current_chunk)

    return chunks


def process_uploaded_file(file, upload_folder, collection, embedding_generator):
    # 保存文件
    filename = os.path.join(upload_folder, file.filename)
    file.save(filename)

    # 处理文件
    if filename.lower().endswith('.pdf'):
        text = process_pdf(filename)
    else:  # .txt
        text = process_txt(filename)

    # 分块文本 (使用新算法)
    chunks = chunk_text(text, min_chunk_size=200, max_chunk_size=500, overlap=50)

    # 生成嵌入向量
    embeddings = embedding_generator(chunks)

    # 存储向量
    from milvus_manager import store_vectors
    ids = store_vectors(chunks, embeddings, collection)

    # 删除临时文件
    os.remove(filename)

    return {
        "status": "success",
        "message": f"文件处理成功，已存储 {len(chunks)} 个片段",
        "chunks_count": len(chunks)
    }