from sentence_transformers import SentenceTransformer
import logging

# 全局嵌入模型
embedding_model = None


def init_embedding_model():
    global embedding_model
    try:
        # 加载本地BGE-M3模型
        embedding_model = SentenceTransformer(r"D:\model\BAAI\bge-m3")
        return embedding_model
    except Exception as e:
        logging.error(f"Embedding model initialization error: {str(e)}")
        raise


def generate_embeddings(texts):
    global embedding_model
    if embedding_model is None:
        init_embedding_model()

    try:
        embeddings = embedding_model.encode(texts)
        return embeddings
    except Exception as e:
        logging.error(f"Error generating embeddings: {str(e)}")
        raise