import logging
import numpy as np
from pymilvus import Collection


def search_similar(query, collection, embedding_generator, top_k=10):
    """
    在Milvus中搜索与查询相似的文本

    参数:
        query: 查询文本
        collection: Milvus集合对象
        embedding_generator: 嵌入生成函数
        top_k: 返回的结果数量

    返回:
        相似文本列表，每个元素包含文本内容、距离和ID
    """
    try:
        # 生成查询向量
        query_embedding = embedding_generator([query])

        # 确保嵌入是NumPy数组
        if isinstance(query_embedding, list):
            query_embedding = np.array(query_embedding)

        # 检查向量维度是否匹配（使用正确的字段访问方式）
        dim = None
        for field in collection.schema.fields:
            if field.name == "vector":
                dim = field.dim
                break

        if dim is None:
            logging.error("在集合中找不到向量字段")
            return []

        if query_embedding.shape[1] != dim:
            logging.error(f"向量维度不匹配! 查询向量维度: {query_embedding.shape[1]}, 集合维度: {dim}")
            return []

        # 搜索参数
        search_params = {
            "metric_type": "L2",
            "params": {"nprobe": 10}
        }

        # 执行搜索
        results = collection.search(
            data=[query_embedding.tolist()[0]],  # 确保是列表的列表
            anns_field="vector",
            param=search_params,
            limit=top_k,
            output_fields=["text"]
        )

        # 处理结果（添加完整的返回信息）
        similar_texts = []
        for hits in results:
            for hit in hits:
                similar_texts.append({
                    "text": hit.entity.get("text"),
                    "distance": hit.distance,  # 添加距离信息
                    "id": hit.id  # 添加ID信息
                })

        return similar_texts

    except Exception as e:
        logging.error(f"向量搜索失败: {str(e)}", exc_info=True)
        return []