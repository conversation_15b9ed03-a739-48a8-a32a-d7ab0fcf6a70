import os
import logging
from flask import Flask, request, jsonify, render_template
from flask_cors import CORS
from file_processor import allowed_file, process_uploaded_file
from vector_search import search_similar
from llm_client import generate_answer
from milvus_manager import collection
from embeddings import generate_embeddings, init_embedding_model
from milvus_manager import init_milvus, store_vectors
from llm_client import init_llm_client
from reranker import rerank_documents  # 修改为本地重排序函数

app = Flask(__name__)
CORS(app)

# 配置
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'txt', 'pdf'}
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

# 确保上传文件夹存在
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# 初始化日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局组件
collection = None
embedding_model = None
llm_client = None


def initialize_application():
    """初始化应用组件"""
    global collection, embedding_model, llm_client

    try:
        logger.info("Initializing application components...")

        # 初始化Milvus
        collection = init_milvus()
        logger.info("Milvus initialized successfully")

        # 初始化嵌入模型
        embedding_model = init_embedding_model()
        logger.info("Embedding model initialized successfully")

        # 初始化大模型客户端
        llm_client = init_llm_client()
        logger.info("LLM client initialized successfully")

        logger.info("All components initialized successfully")
        return True
    except Exception as e:
        logger.error(f"Failed to initialize components: {str(e)}")
        return False


# 在应用启动前初始化组件
initialize_application()


# 路由
@app.route('/')
def index():
    return render_template('index.html')


@app.route('/upload', methods=['POST'])
def upload_file():
    if 'file' not in request.files:
        return jsonify({"status": "error", "message": "没有文件部分"})

    file = request.files['file']

    if file.filename == '':
        return jsonify({"status": "error", "message": "没有选择文件"})

    if file and allowed_file(file.filename, ALLOWED_EXTENSIONS):
        try:
            result = process_uploaded_file(
                file,
                app.config['UPLOAD_FOLDER'],
                collection,
                generate_embeddings
            )
            return jsonify(result)
        except Exception as e:
            return jsonify({"status": "error", "message": f"处理文件时出错: {str(e)}"})

    return jsonify({"status": "error", "message": "不支持的文件类型"})


@app.route('/query', methods=['POST'])
def handle_query():
    data = request.json
    if not data or 'query' not in data:
        return jsonify({"status": "error", "message": "缺少查询内容"})

    query_text = data['query']

    try:
        # 1. 向量搜索（获取更多结果）
        initial_results = search_similar(query_text, collection, generate_embeddings, top_k=10)  # 获取10个结果

        if not initial_results:
            return jsonify({
                "status": "success",
                "has_context": False,
                "answer": "没有找到相关信息来回答这个问题。请先上传相关文件。"
            })

        # 2. 文档重排序（本地模型调用）
        reranked_results = rerank_documents(
            query_text,
            initial_results,
            top_n=3
        )

        # 3. 生成答案
        answer = generate_answer(query_text, reranked_results, llm_client)

        # 获取实际使用的模型名称
        used_model = reranked_results[0].get("rerank_model", "local-bge-reranker-v2-m3") if reranked_results else "local-bge-reranker-v2-m3"

        return jsonify({
            "status": "success",
            "has_context": True,
            "context": reranked_results,
            "answer": answer,
            "rerank_model": used_model
        })
    except Exception as e:
        return jsonify({"status": "error", "message": f"处理查询时出错: {str(e)}"})


if __name__ == '__main__':
    # 确保组件已初始化
    if not initialize_application():
        logger.error("应用组件初始化失败，部分功能可能不可用")

    app.run(debug=True)