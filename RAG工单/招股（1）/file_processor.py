import os
import re
import fitz  # PyMuPDF for PDF processing

def allowed_file(filename, allowed_extensions):
    return '.' in filename and \
        filename.rsplit('.', 1)[1].lower() in allowed_extensions

def process_pdf(file_path):
    try:
        doc = fitz.open(file_path)
        text = ""
        for page in doc:
            text += page.get_text()
        # 清理文本
        text = re.sub(r'\s+', ' ', text).strip()
        return text
    except Exception as e:
        raise Exception(f"Error processing PDF: {str(e)}")

def process_txt(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            text = f.read()
        # 清理文本
        text = re.sub(r'\s+', ' ', text).strip()
        return text
    except Exception as e:
        raise Exception(f"Error processing TXT: {str(e)}")

def chunk_text(text, chunk_size=500, overlap=50):
    chunks = []
    start = 0
    while start < len(text):
        end = start + chunk_size
        chunk = text[start:end]
        chunks.append(chunk)
        start = end - overlap
    return chunks

def process_uploaded_file(file, upload_folder, collection, embedding_generator):
    # 保存文件
    filename = os.path.join(upload_folder, file.filename)
    file.save(filename)

    # 处理文件
    if filename.lower().endswith('.pdf'):
        text = process_pdf(filename)
    else:  # .txt
        text = process_txt(filename)

    # 分块文本
    chunks = chunk_text(text)

    # 生成嵌入向量
    embeddings = embedding_generator(chunks)

    # 存储向量
    from milvus_manager import store_vectors
    ids = store_vectors(chunks, embeddings, collection)

    # 删除临时文件
    os.remove(filename)

    return {
        "status": "success",
        "message": f"文件处理成功，已存储 {len(chunks)} 个片段",
        "chunks_count": len(chunks)
    }