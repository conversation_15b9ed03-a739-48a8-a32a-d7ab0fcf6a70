import logging
import numpy as np


def search_similar(query, collection, embedding_generator, top_k=10):
    """
    在Milvus中搜索与查询相似的文本

    参数:
        query: 查询文本
        collection: Milvus集合对象
        embedding_generator: 嵌入生成函数
        top_k: 返回的结果数量

    返回:
        相似文本列表，每个元素包含文本内容和距离
    """
    try:
        # 生成查询向量
        query_embedding = embedding_generator([query])

        # 确保嵌入是NumPy数组
        if isinstance(query_embedding, list):
            query_embedding = np.array(query_embedding)

        # 搜索参数
        search_params = {
            "metric_type": "L2",
            "params": {"nprobe": 10}
        }

        # 执行搜索
        results = collection.search(
            data=[query_embedding.tolist()[0]],  # 确保是列表的列表
            anns_field="vector",
            param=search_params,
            limit=top_k,
            output_fields=["text"]
        )

        # 处理结果
        similar_texts = []
        for hits in results:
            for hit in hits:
                similar_texts.append({
                    "text": hit.entity.get("text"),
                    "distance": hit.distance
                })

        return similar_texts
    except Exception as e:
        logging.error(f"向量搜索错误: {str(e)}")
        raise