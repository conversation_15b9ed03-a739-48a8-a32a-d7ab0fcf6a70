<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能文档问答系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#10B981',
                        neutral: '#64748B',
                        dark: '#1E293B',
                        light: '#F8FAFC'
                    },
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .shadow-soft {
                box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
            }
            .transition-custom {
                transition: all 0.3s ease;
            }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen font-sans">
    <header class="bg-white shadow-sm sticky top-0 z-10">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <i class="fa fa-file-text-o text-primary text-2xl"></i>
                <h1 class="text-xl font-bold text-dark">智能文档问答系统</h1>
            </div>
            <div class="text-sm text-neutral">
                基于BGE-M3和Milvus
            </div>
        </div>
    </header>

    <main class="container mx-auto px-4 py-8 max-w-6xl">
        <!-- 文件上传区域 -->
        <section class="bg-white rounded-xl shadow-soft p-6 mb-8">
            <h2 class="text-lg font-semibold mb-4 flex items-center">
                <i class="fa fa-cloud-upload text-primary mr-2"></i>
                上传文档
            </h2>
            <p class="text-neutral text-sm mb-4">支持上传PDF和TXT文件，系统将处理并存储文档内容以便后续查询</p>

            <div id="drop-area" class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center transition-custom hover:border-primary">
                <i class="fa fa-file-text-o text-4xl text-neutral mb-3"></i>
                <p class="mb-2">拖放文件到此处，或</p>
                <label class="inline-block bg-primary text-white px-4 py-2 rounded-md cursor-pointer hover:bg-primary/90 transition-custom">
                    <i class="fa fa-folder-open mr-1"></i> 选择文件
                    <input type="file" id="file-input" class="hidden" accept=".pdf,.txt">
                </label>
                <p class="text-xs text-neutral mt-3">支持 PDF, TXT 格式</p>
            </div>

            <div id="upload-status" class="mt-4 hidden">
                <div class="flex items-center">
                    <i id="status-icon" class="mr-2"></i>
                    <span id="status-message"></span>
                </div>
                <div id="progress-container" class="mt-2 h-2 bg-gray-200 rounded-full overflow-hidden hidden">
                    <div id="progress-bar" class="h-full bg-secondary w-0 transition-all duration-300"></div>
                </div>
            </div>
        </section>

        <!-- 查询区域 -->
        <section class="bg-white rounded-xl shadow-soft p-6 mb-8">
            <h2 class="text-lg font-semibold mb-4 flex items-center">
                <i class="fa fa-search text-primary mr-2"></i>
                文档查询
            </h2>
            <p class="text-neutral text-sm mb-4">基于已上传的文档内容进行智能问答</p>

            <div class="flex flex-col md:flex-row gap-4">
                <div class="flex-1">
                    <textarea
                        id="query-input"
                        class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary outline-none transition-custom resize-none"
                        rows="3"
                        placeholder="请输入您的问题..."></textarea>
                </div>
                <div class="md:w-40 flex items-end">
                    <button
                        id="query-button"
                        class="w-full bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-custom flex items-center justify-center"
                        disabled>
                        <i class="fa fa-paper-plane mr-2"></i>
                        提问
                    </button>
                </div>
            </div>
        </section>

        <!-- 结果区域 -->
        <section id="results-section" class="bg-white rounded-xl shadow-soft p-6 hidden">
            <h2 class="text-lg font-semibold mb-4 flex items-center">
                <i class="fa fa-comment-o text-primary mr-2"></i>
                回答结果
            </h2>

            <div id="loading-indicator" class="py-8 text-center hidden">
                <i class="fa fa-circle-o-notch fa-spin text-primary text-2xl"></i>
                <p class="mt-2 text-neutral">正在处理您的请求...</p>
            </div>

            <div id="answer-container" class="prose max-w-none">
                <!-- 回答将在这里显示 -->
            </div>

            <div id="context-section" class="mt-6 pt-6 border-t border-gray-100">
                <h3 class="text-md font-medium mb-3 text-neutral">参考文档片段：</h3>
                <div id="context-container" class="space-y-3 text-sm text-gray-700">
                    <!-- 参考上下文将在这里显示 -->
                </div>
            </div>
        </section>
    </main>

    <footer class="bg-white border-t border-gray-200 py-6 mt-12">
        <div class="container mx-auto px-4 text-center text-neutral text-sm">
            <p>智能文档问答系统 &copy; 2023</p>
        </div>
    </footer>

    <script>
        // DOM元素
        const dropArea = document.getElementById('drop-area');
        const fileInput = document.getElementById('file-input');
        const uploadStatus = document.getElementById('upload-status');
        const statusIcon = document.getElementById('status-icon');
        const statusMessage = document.getElementById('status-message');
        const progressContainer = document.getElementById('progress-container');
        const progressBar = document.getElementById('progress-bar');
        const queryInput = document.getElementById('query-input');
        const queryButton = document.getElementById('query-button');
        const resultsSection = document.getElementById('results-section');
        const loadingIndicator = document.getElementById('loading-indicator');
        const answerContainer = document.getElementById('answer-container');
        const contextContainer = document.getElementById('context-container');

        // 检查是否有上传的文件，启用/禁用查询按钮
        function updateQueryButtonState() {
            queryButton.disabled = queryInput.value.trim() === '';
        }

        // 显示上传状态
        function showUploadStatus(message, isError = false, progress = null) {
            uploadStatus.classList.remove('hidden');
            statusMessage.textContent = message;

            if (isError) {
                statusIcon.className = 'fa fa-exclamation-circle text-red-500';
            } else {
                statusIcon.className = 'fa fa-info-circle text-primary';
            }

            if (progress !== null) {
                progressContainer.classList.remove('hidden');
                progressBar.style.width = `${progress}%`;
            } else {
                progressContainer.classList.add('hidden');
            }
        }

        // 处理文件上传
        function handleFileUpload(file) {
            if (!file) return;

            // 检查文件类型
            const fileExt = file.name.split('.').pop().toLowerCase();
            if (!['pdf', 'txt'].includes(fileExt)) {
                showUploadStatus('不支持的文件类型，请上传PDF或TXT文件', true);
                return;
            }

            showUploadStatus('正在上传并处理文件...', false, 30);

            const formData = new FormData();
            formData.append('file', file);

            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('上传失败');
                }
                return response.json();
            })
            .then(data => {
                if (data.status === 'success') {
                    showUploadStatus(data.message, false, 100);
                    // 重置文件输入
                    fileInput.value = '';
                } else {
                    showUploadStatus(data.message, true);
                }
            })
            .catch(error => {
                showUploadStatus(`上传失败: ${error.message}`, true);
            });
        }

        // 处理查询
        function handleQuery() {
            const queryText = queryInput.value.trim();
            if (!queryText) return;

            resultsSection.classList.remove('hidden');
            loadingIndicator.classList.remove('hidden');
            answerContainer.innerHTML = '';
            contextContainer.innerHTML = '';

            fetch('/query', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ query: queryText })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('查询失败');
                }
                return response.json();
            })
            .then(data => {
                loadingIndicator.classList.add('hidden');

                if (data.status === 'success') {
                    answerContainer.innerHTML = `<p>${data.answer}</p>`;

                    if (data.has_context && data.context && data.context.length > 0) {
                        data.context.forEach((item, index) => {
                            const contextItem = document.createElement('div');
                            contextItem.className = 'p-3 bg-gray-50 rounded border border-gray-100';
                            contextItem.innerHTML = `
                                <p class="mb-1">${item.text}</p>
                                <p class="text-xs text-neutral">相似度: ${(1 - item.distance).toFixed(4)}</p>
                            `;
                            contextContainer.appendChild(contextItem);
                        });
                    } else {
                        document.getElementById('context-section').classList.add('hidden');
                    }
                } else {
                    answerContainer.innerHTML = `<p class="text-red-500">${data.message}</p>`;
                }
            })
            .catch(error => {
                loadingIndicator.classList.add('hidden');
                answerContainer.innerHTML = `<p class="text-red-500">查询失败: ${error.message}</p>`;
            });
        }

        // 事件监听
        dropArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropArea.classList.add('border-primary');
        });

        dropArea.addEventListener('dragleave', () => {
            dropArea.classList.remove('border-primary');
        });

        dropArea.addEventListener('drop', (e) => {
            e.preventDefault();
            dropArea.classList.remove('border-primary');

            if (e.dataTransfer.files.length) {
                handleFileUpload(e.dataTransfer.files[0]);
            }
        });

        fileInput.addEventListener('change', () => {
            if (fileInput.files.length) {
                handleFileUpload(fileInput.files[0]);
            }
        });

        dropArea.addEventListener('click', () => {
            fileInput.click();
        });

        queryInput.addEventListener('input', updateQueryButtonState);
        queryButton.addEventListener('click', handleQuery);
        queryInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                if (!queryButton.disabled) {
                    handleQuery();
                }
            }
        });

        // 初始化
        updateQueryButtonState();
    </script>
</body>
</html>
