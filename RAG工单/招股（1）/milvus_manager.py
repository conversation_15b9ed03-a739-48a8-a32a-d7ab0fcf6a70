from pymilvus import connections, FieldSchema, CollectionSchema, DataType, Collection, utility
import logging

# 全局Milvus集合
collection = None

def init_milvus():
    global collection
    try:
        # 连接到Milvus
        connections.connect("default", host="localhost", port="19530")

        # 定义集合名称
        collection_name = "document_vectors"

        # 检查集合是否已存在，不存在则创建
        if not utility.has_collection(collection_name):
            # 定义字段
            fields = [
                FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
                FieldSchema(name="text", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=1024)  # BGE-M3输出维度为1024
            ]

            # 创建集合 schema
            schema = CollectionSchema(fields, "Document vectors for similarity search")

            # 创建集合
            collection = Collection(collection_name, schema)

            # 创建索引
            index_params = {
                "index_type": "IVF_FLAT",
                "metric_type": "L2",
                "params": {"nlist": 128}
            }
            collection.create_index("vector", index_params)
        else:
            collection = Collection(collection_name)

        # 加载集合
        collection.load()
        return collection
    except Exception as e:
        logging.error(f"Milvus initialization error: {str(e)}")
        raise

def store_vectors(texts, embeddings, collection):
    try:
        data = [
            texts,  # text字段
            embeddings.tolist()  # vector字段
        ]
        mr = collection.insert(data)
        collection.flush()
        return mr.primary_keys
    except Exception as e:
        logging.error(f"Error storing vectors: {str(e)}")
        raise