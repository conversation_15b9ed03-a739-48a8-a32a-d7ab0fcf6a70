https://zhuanlan.zhihu.com/p/1901014191235633835

qwen3:
先是有一个输入，输入先进行分词，去除停用词，数据清洗等，经过embedding层给他转成向量，
Embedding：是一个矩阵，行数是分词的token数，
列数是模型定义的通常为512,1024
vocab_size 保存了每个 token 和 token_id ,
输入一段话通过nn.Embedding 映射成高维向量矩阵,
通过token_id 获取矩阵的第几行 嵌入到下一层。

然后通过旋转位置编码将绝对位置信息融入到自注意力机制力，
计算方法是通过矩阵的按位相乘和向量加法，
RotaryEmbedding(旋转位置编码)：为模型中每个token提供在序列中绝对位置信息，
transformer的正余弦处理长序列和精确相对位置建模存在局限
RoPE 的洞察： 与其将位置编码“加”到词嵌入上，
不如通过一个与位置相关的旋转操作来“变换”词嵌入向量本身。
这个旋转操作能自然地保持向量的模长不变，同时改变其方向，而方向的变化就确定编码了位置信息。

然后到了自注意力机制，
在计算之前还需要经过一个前置的归一化，
注意力机制的计算与transformer不同，
在qwen3中注意力计算的Q和K是需要经过归一化在进行计算的，
并且计算的方法也是不同的，
qwen3是分组查询注意力机制（GQA）
在transformer中使用的是多头注意力计算，
qwen3中使用的是QKA计算，这里面的K,V是共享多个Q的，
所以说他的计算量是不变的，但是却减少了k和v的内存占用，

然后经过attention层之后要再经过一个前置归一化之后经过一个残差链接在进入到MLP层中，
MLP层就是由三个线性层组成的，他的前两个线性层的作用是起到了升维的作用，
分别是Gate和up层，其中GAte层要用到一个激活函数SiLu,GAte和up做一个矩阵的按位相乘，
之后经过一个Down层，起到了降维的作用，再然后经过一个残差，再来说一下MOE,

qwen3模型分为多个block，每个block包含一个注意力层，一个MLP层，
每个block前面是前置归一化，后面是一个残差链接，最后一个block
会经过一个LLMhead,他的作用是将矩阵转换为token会做一个维度转换，

MOE可以理解为一个混合专家，它是由多个专家组成的，
这里的专家指的就是MLP，他是由一个门控网络根据你的输入去选择最相关的专家来进行输出的，
并且仅计算被选中的专家的输出，其余的专家是直接跳过的，这样就明显的减小了计算量，
比如每个专家代表一种领域，门控网络会选择最相关的专家，
然后是经过一个解码全连接层,这个decoder-layer的输出是一个三维的数据，分别是batch批次的大小，
seq-len序列的长度，通常为512，还有一个是模型的维度，小模型的维度通常为768，大模型的维度是4096，
最后还要经过一个归一化，在进行输出。

模型的量化：是通过降低模型数值的精度，比如说原来是一个pf16的数据，
现在把它降为int4的数据精度，他的大小是原来的四分之一，但是在量化的过程中不会量化一些重要的数值，
所以说他的大小是原来的3分之一，减少模型中数值（权重、激活值等）的表示精度（比特位数）。

模型的蒸馏： 训练一个小的、高效的模型（学生模型）去模仿一个大的、更复杂的模型（教师模型）的行为或知识。
目标： 得到一个比单独训练更小、更快、但性能接近甚至有时超越教师模型的学生模型。重点是知识迁移而不仅仅是数值精度降低

qwen3最大模型:qwen3 - 235B -A22B