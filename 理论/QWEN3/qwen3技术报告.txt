https://zhuanlan.zhihu.com/p/1905945819108079268

GRPO
https://zhuanlan.zhihu.com/p/20812786520


模型的架构：
Qwen3系列包括6个密集架构模型（Qwen3-0.6B、Qwen3-1.7B、Qwen3-4B、Qwen3-8B、Qwen3-14B和Qwen3-32B）
和2个MoE模型（Qwen3-30B-A3B和Qwen3-235B-A22B）。

qwen3密集架构模型引入的关键技术：
qwen3 密集架构模型 采用分组查询注意力机制（GQA），SwiGlu激活函数，旋转位置编码（RoPe），前置归一化（RMSNorm）
移除了 QKV偏置 在注意力中引入QK-Norm 确保训练的稳定性

qwen3Moe模型关键技术：
实现了 细粒度专家分割技术，配备 128 个专家，每个token激活 8 个专家，
去除了共享专家机制，使用全局批量负载平衡损失函数，沿用了qwen的tokenizer，
实现了字节级字节对编码（BBPE），词汇表规模为 151669 toekn。

预训练数据：利用qwen2.5识别大量文本提取高质量文本。使用多个不同格式的文本token，
整合更多多语言数据和新的语言，相比之前从 29 种语言增加到 119 种，开发了一套多语言数据标注系统，
提高数据的质量和多样性，


预训练阶段：
 * 第一阶段通用阶段 -- 在 30T token  序列长度为 4096个token 上训练建立坚实的通用知识基础，覆盖 119 种语言
 * 第二阶段推理阶段 -- 在知识密集型数据上记忆布训练增强STEM 和 编程领域推理能力，约 5T 的高质量数据，加速了学习率衰减的过程
 * 第三阶段长上下文阶段 -- 在长上下文数据上训练，将最大上下文长度从 4k 提升至 32k 个token
                        使用 注意力基频扩展（ABF） YARN（） 和 双块注意力（DCA）

预训练评估：
 * 通用任务：MMLU（），MMLU-PRO（），MMLU-redux（），BBH（）
 * 数学与（STEM）任务：GPQA（），GSM8K（），MATH（）
 * 编程任务：EvalPlus（）等
 * 多语言任务：MGSM（）等
            相同的预训练数据下，qwen3MOE模型仅需 1/5 的激活参数 可以qwen3 密集模型架构性能相当
            qwen3MOE 基础模型 不到 1/2 的激活参数和更少的总参数  超过qwen2.5MOE 基础模型
            仅用qwen2.5密集架构基础模型1/10的激活参数 = qwen3MOE基础模型


'''
Qwen3 - 32B -Base 是 Qwen3 系列中最大的密集架构模型
'''

后训练（人类反馈的强化学习||人类对齐）：

优化轻量级，模型的后训练流程，利用大规模模型的知识，
显著降低了小规模模型构建所需要的计算资源和开发工作量。

 * 长思维链冷启动（Long - CoT）：长思维链（Long - CoT） - LLM的思考过程，长思维链要求模型通过多步推理（通常 > 5步）解决复杂问题，
                                数据集构建：查询过滤和响应过滤 主要的数据集：一问一答包含思考的过程，
                                查询过滤：从海量问题中筛选出适合长思维链训练的优质问题。不是所有问题都值得生成长推理链
                                            简单问题会浪费资源，模糊问题会导致错误学习。
                                响应过滤：对生成的思维链响应进行质量过滤，保留逻辑正确、步骤完整的推理链，
                                            模型生成的思维链可能包含逻辑错误、事实错误或跳跃步骤。
 * 推理强化学习：采用 梯度正则化策略优化（GRPO）（群组相对策略优化） 奖励模型（基于规则的奖励，模型型奖励），策略模型（提出问题进行推理，生成一组数据），
                    参考模型（给每个答案评分，计算均值和方差，评分高的奖励，评分低的做出惩罚），
                主要的数据集：编程和数学能力，通过群组评分，GRPO 对每个问题q从原策略模型采样一组输出，
                            基于每组内输出所对应的奖励机制，超越原高质量答案加分，偏离扣分

 * 思考融合模式：将"非思考"整合"思考"模型中，可以控制推理行为，对推理强化学习模型进行 监督微调（SFT）融合两种模式的聊天模版，
        SFT数据构建：结合"思考"和"非思考"两类数据
        聊天模版设计:默认情况下以思考模式运行
 * 通用RL：建立了二十多种不同任务的复杂奖励系统，针对以下核心能力的增强：
        指令遵循能力：确保模型准确理解并执行用户指令
        格式规范遵守：遵守特定的格式规范
        偏好一致性：开放式查询，更自然，更满意的体验
        智能体能力：通过指定接口准确调用工具
        专业场景适应能力：三种奖励机制
                        基于规则的奖励：定制一套规则，满足加分，高精度评估模型输出的正确性（验证标准答案），
                        基于参考答案的模型奖励：给每个模型提供参考答案，通过相似度判断，进行评分（给每一个答案打分，引导模型的价值观,跟人类对齐）
                        无参考答案的模型奖励：通过人类主管感受，或用另一个模型进行评估来打分
        强到弱的知识蒸馏：专为优化轻量级模型而设计，涵盖 5 个密集架构模型和 1 个Moe 模型，蒸馏过程分为两个阶段，
                    离策略蒸馏：结合教师模型在"思考"和"非思考"模式下生成，帮助轻量级学生模型建立基本推理技能和不同模式的切换，
                    在策略蒸馏：学生模型首先采样提示词，在"思考"和"非思考"模式下响应，通过学生模型的logits
                    （在最终输出层产生的原始、未经过归一化的得分值）和教师模型logits对齐微调学生模型，

                    '''light weight :知识蒸馏（大模型生成高质量的问答对，在小模型上做SFT）成本比RL要低，效果比RL好'''

'''强化学习（RL)L: PPO 近端策略优化 （Policy model，Reference model，Value model，Reward model）
            DPO 直接偏好优化 （off policy）
            GRPO 组相对策略优化（Policy model，Reference model，Reward model）'''


后训练评估：
 * 通用任务： MMLU-redux ...... 等
 * 对齐评估：Arena-Hard和AlignBench v1.1进行评估；写作能力则通过Creative Writing V3和WritingBench评估模型的熟练度和创造力。
 * 数学与文本推理：MATH - 500
 * 多语言能力：涵盖四种任务
        指令遵循：Multi-IF
        知识评估：INCLUDE 和 MMMLU
        数学任务：MT - AIME2024 和 PolyMath
        逻辑推理：MlogiQA

评估结果：
    旗舰模型Qwen3-235B-A22B 在"思考"和"非思考"模式下性能超越 deepseek-R1 和Deepseek-V3
    旗舰密集模型Qwen3 - 32B 与Open-AI-o3 mini 性能相当
    轻量级模型 验证了强到弱的知识蒸馏方法有效性


