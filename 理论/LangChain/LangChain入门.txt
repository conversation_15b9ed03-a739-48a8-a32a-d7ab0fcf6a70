https://zhuanlan.zhihu.com/p/1905282253728219475

手把手教你用 LangChain 高效加载 PDF
https://blog.csdn.net/The_Thieves/article/details/148745285

LangChain 六大模块：Agent，模型I/O，记忆，链，回调处理器，数据增强

模型I/O：模型的封装，模型的调用，
数据增强：支出 PDF，WORD，EXCEL，加载，转换，存储，查询，进行文档切割，
            转换等操作   '''PDF fitz 可以提取PDF的图片 '''
链：自动调用大模型
记忆：保存最近的聊天记录，每次聊天将聊天记录放到提示词模版，实现多轮对话
Agent：核心思想是利用LLM选择操作序列。在链中，操作序列是硬编码的，
而在Agent代理中，大语言模型被用作推理引擎，确定执行哪些操作，以及它们的执行顺序。
回调处理器：允许开发者在LLM应用的各个阶段对状态进行干预。这对于日志记录、
监视、流处理等任务非常有用。通过API提供的callbacks参数，开发者可以订阅这些事件。

RAG框架
'''
LangChain，LLamaIndex，dify，RAGAnything
'''
Agent框架：
'''
AutoGen，LangGraph，Coze，MCP协议，OpenManus
'''