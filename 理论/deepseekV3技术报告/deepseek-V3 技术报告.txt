
https://zhuanlan.zhihu.com/p/14890557782


Deepseek-V3 借鉴Deepseek-R1 所使用的强化学习（RL）技术
Deepseek-V3 是一款 混合专家（MoE）语言模型，参数规模达到 671B 每个 token 激活参数量为 37B

关键技术：
延续之前 多头潜在注意力机制（MLA）确保推理效率，使用DeepseekMoE高效训练
创新技术 -- 采用无辅助损失的负载均衡策略，采用多 token 预测训练目标（MTP）
            采用 FP8混合精度训练技术（激活值以 8 位浮点存储），提升了训练速度，降低了GPU内存占用
预训练阶段 充分利用了 infiniBand（IB） 和 NVLinK 的带宽性能，无序依赖高昂的张量并行技术
后训练阶段 将具有思维链（CoT）模型比如（Deepseek-R1）的推理能力转移到标准的LLM中，

基本架构：
    *   Deepseek-V3 基础架构建立在 Transform 框架之上，
    *   采用 多头潜在注意力机制（MLA） 和 DeepseekMoE 技术
        MLA：对于注意力键和值进行低秩联合压缩，减低推理过程中的键值缓存开销,对q也进行压缩，但是q不参与内存占用

        DeepseekMoE：采用更细粒度专家分配机制，将部分专家设置为共享专家
    *   无辅助损失负载均衡策略:为每个专家引入了一个 偏置项，并添加相应的 亲和度分数
        训练过程中系统会监控所有批次的专家负载分布，每个步骤结束时对于负载高的专家，偏置项 会减少，负载不足的专家偏置项增加
    *   序列级辅助损失补充机制：为了防止单个序列出现负载不均衡现象
    *   节点约束路由机制：系统限制每个token最多分配给M个计算节点
        在约束下，MoE训练框架能够实现计算与通信的近乎完全并行处理
    *   完整的Token保留机制：保留完整Token
    *   多 Token 预测机制（MTP）：将预测范围扩展到每个位置后的多个 Token，提高数据利用率，使模型提前规划，更准确预测后续token

基础设施：
配备 2048 个 NVIDIA H800 GPU 每个计算节点配备8个GPU 节点内的 NVLink 和 NVSwitch 实现高速互联 节点之间采用 InfiniBand（IB）实现高效通信
    *   训练框架：自主研发的 HAI-LLM 并行策略的三个层面： 16路流水线并行（PP），跨8个节点64路专家并行（EP），ZeRO-1的数据并行（DP）
        优化：
            --- 开发了DualPipe 流水线并行算法，减少了流水线停滞现象，实现了前向和后向过程中计算与通信阶段的重叠
            --- 优化了跨节点全队全通信内核，充分利用IB和NVLink带宽，减少通信所需的流逝多处理器（SMS）的资源占用
            --- 精细内存管理，使模型训练无需依赖开销较大的张量并行（TP）技术
    *   FP8 混合精度框架训练：deepseek首次采用，FP8通用矩阵乘法（GEMM）突破了跨节点MoE训练中的通讯瓶颈 前向传播（Fprop），激活值反向传播（Dgrad），权重反向传播（Wgrad）
                            均采用 FP8 将计算速度提升至原有 BF16 方法的两倍，大幅降低内存的使用量
            --- 量化和乘法精度优化
            --- 细粒度量化技术
            --- 累积精度优化
            --- 低精度存储与通信优化
            --- 优化器状态的精度优化
            --- 激活值的精度优化
            --- 低精度通信优化

    *   推理和部署：Deepseek-V3 部署在 H800 集群上，每个节点内的GPU通过NVLink连接，集群内的GPU通过 IB 实现全链接
                    为实现服务质量（SLO）和高吞吐量 采用了预填充和解码 分离的部署策略
            --- 预填充：注意力机制采用 4路张量并行（TP4）配合 序列并行（SP） 结合 8路数据并行（DP8）
                        MoE 采用 32路专家并行（EP32） 为实现Moe 部分专家间的负载平衡，采用了冗余专家部署策略
                        配置了 32 个冗余专家，每个GPU除了原有的8个专家，还分配了一个额外的冗余专家，
                        专家动态冗余机制 使每个GPU分配更多专家，但每次推理仅激活 其中的9个
            --- 解码：系统将共享专家作为一种路由专家处理，每个token在路由时会选择9个专家，其中共享专家被视为必然选择的高负载专家

预训练：
    *   数据构建：提升了 数学和编程，扩大了英语和中文之外的多语言覆盖 Deepseek-V3 采用 填充中间（FIM）基于上下文准确预测中间文本
                词表大小：128k 字节级 BPE tokenizer 引入标点符号和换行组合 token
   *    超参数设置：
            --- 模型架构参数：采用61层 Transformer结构 ，隐藏维度 7168 在多头潜在注意力（MLA）中 注意力头数 128，每个头维度 128，
                                KV 压缩维度 512 查询（Q）压缩维度 1536 解耦的查询和键部分 每个头维度为 64
                                除前三层 所有前馈层（FFN）替换 MoE 层每个MoE 配备 1 个共享专家和 256 个路由专家 专家隐藏维度为 2048
            --- 训练参数：优化器 0.9,0.95 权重衰减0.1，预训练最大序列长度 4k 在 14.8T token 上进行训练
    *   长下文扩展：第一阶段将序列长度设为 32k 批量大小为 1920 第二阶段提升至 128k，批量大小 480
    *   评估：采用集成的 HAI-LLM 框架 内部评估：
            --- 多学科选择题评估：MMLU、MMLU Redux、MMLU-Pro、MMMLU、C-Eval 和 CMMLU
            --- 语言理解与推理能力：HellaSwag、PIQA、ARC 和 BigBench Hard (BBH)
            --- 知识问答评估：TriviaQA 和 NaturalQuestions
            --- 阅读理解测试：RACE、DROP、C3 和 CMRC
            --- 指代消歧任务：CLUEWSC 和 WinoGrande
            --- 语言建模评估：Pile 中文理解与文化认知：CCPM
            --- 数学能力测试：GSM8K、MATH、MGSM 和 CMath
            --- 编程能力评估：HumanEval、LiveCodeBench-Base(0801-1101)、MBPP 和 CRUXEval
            --- 综合能力测试：AGIEval（包含英语和中文两个子集）

后训练：
    *   监督微调（SFT）：包含150万个多领域实例数据集
            --- 推理数据处理：采用Deepseek-R1生成数据 具体采用SFT 和 RL 结合训练专家模型 随后作为数据生成器
                                数据格式分为两类 问题与答案配对（问答对） ， 引入系统提示词将问题与答案结合
                                具有思维链的问答对，增强推理能力
            --- 非推理数据处理： 采用Deepseek - V2.5 生成响应 通过人工标注确保数据质量
            --- SFT 训练配置：DeepSeek-V3-Base 进行两轮SFT数据集训练，采用余弦衰减学习率调度策略，训练过程采用多样本序列打包技术
    *   强化学习（RL）：
            --- 奖励模型设计：采用规则型和模型两种奖励模型
                    规则型奖励模型：在具有确定性答案的数学问题，要求模型在特定格式给出最终答案
                    模型型奖励模型：对于答案灵活的问题时，基于问题的回答给出评分
            --- 群组相对策略优化（GRPO）：采用 梯度正则化策略优化（GRPO）（群组相对策略优化） 奖励模型（基于规则的奖励，模型型奖励），策略模型（提出问题进行推理，生成一组数据），
                    参考模型（给每个答案评分，计算均值和方差，评分高的奖励，评分低的做出惩罚），
                主要的数据集：编程和数学能力，通过群组评分，GRPO 对每个问题q从原策略模型采样一组输出，
                            基于每组内输出所对应的奖励机制，超越原高质量答案加分，偏离扣分
    *   监督微调（SFT）：
            --- 通用知识的SFT
    *   强化学习（RL）：
            ——通用领域的强化学习包含各个领域，奖励模型是模型型的奖励模型
    *   知识蒸馏：深度迁移 DeepSeek-R1 的推理能力
            ---专家模型训练：针对数学/代码等场景，训练 领域专家模型（SFT + RL 结合）；
            ---数据生成：用专家模型为 SFT 数据生成 高质量候选答案；
            ---拒绝采样（Rejection Sampling）：筛选保留 R1 优点（强推理）且输出简洁的答案 → 作为最终 SFT 数据
    *   评估：
            --- 标准评估：性能与GPT-4o 相当
            --- 英语能力：MMLU中 超越qwen2.-72B
                        MMLU-Pro 教育知识评测中 仅次于 Claude-Sonnet3.5
            --- 代码与数学能力：涵盖工程实践和算法编程 超越众多开源模型，充分验证Deepseek-R1知识蒸馏技术的有效性
            --- 中文能力：在中文教育知识评估（C-Eval） 和 中文指代消歧挑战（CLUEWSC）达到较高水平
