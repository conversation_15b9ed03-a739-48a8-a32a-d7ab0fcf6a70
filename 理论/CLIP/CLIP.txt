https://zhuanlan.zhihu.com/p/493489688

CLIP是用文本作为监督信号来训练可迁移的视觉模型,即一种基于对比文本-图像对的预训练方法或者模型
CLIP包括两个模型：
    *   Text Encoder:提取文本的特征，常用NLP的 text Transform
    *   Image Encoder：提取图片的特征，常用的 CNN（卷积），Vision Transform

计算文本特征和图像特征的余弦相似性
CLIP的过程：
2. 推理阶段：Zero-Shot 分类流程
    构建文本提示：将分类标签转化为自然语言描述（如“一张{category}的照片”），生成候选类别的文本特征18。
    提取图像特征：输入待分类图像，通过图像编码器得到特征向量。
    相似度匹配：计算图像特征与所有文本特征的余弦相似度，最高相似度对应的文本类别即为预测结果

图像搜索：基于文本来搜索图像是CLIP最能直接实现的一个应用
视频理解：CLIP是基于文本-图像对来做的，也可以扩展到文本-视频，比如VideoCLIP
图像编辑：HairCLIP用来定制发型
图像生成：StyleCLIP，CLIP-GEN 实现图像生成
自监督学习：华为的MVP工作采用CLIP的视觉自监督训练
VL任务：图像描述，视觉问答
