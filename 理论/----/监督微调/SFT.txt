https://zhuanlan.zhihu.com/p/702629428

llama factory LoRA微调qwen大模型
https://blog.csdn.net/m0_73365120/article/details/141872756?fromshare=blogdetail&sharetype=blogdetail&sharerId=141872756&sharerefer=PC&sharesource=2503_90140315&sharefrom=from_link


微调 ：微调的使用场景有后训练阶段和应用阶段，它实现的效果和RAG是一样的，
rag是通过外挂一个知识库实现的，微调是通过训练让模型本身学习了这个知识，微调的类型有两个，
全参数微调和高效参数微调，全参数微调使用模型所有参数进行微调使其适应我们的任务目标，
高效参数微调是经过冻结全部训练权重，用小部分参数进行微调，
全参微调和Lora微调的对比：
全参 -- 占用显存高，成本高。训练时间长理论来说效果会好
LoRA -- 占用显存低，成本低，训练时间短
LoRA微调：
微调的参数：学习率（rate），批次大小(batch_size)，训练轮数(epochs)，优化器，正则化(dropout)
*   参数配置:缩放因子（alpha （2 * r））, 秩（r,控制低维矩阵的维度决定参数的训练量），dropout（防止过拟合）
数据：高质量问答对（json格式）。后训练：数据分布要广泛，各方面的数据都要有。
应用：某个行业的专业数据。加一些通用数据(配比一些通用数据30%)
解决的问题：数据的时效性，专业性，大模型的幻觉
选择工具：LLamaFactory

RAG:外挂一个知识库 ---起效快，拿到数据后，可以快速更新知识库
SFT：训练模型，让模型学到最新的知识 ---需要花时间训练，然后评测 理论上说效果更好


LoRA 的本质就是用更少的训练参数来近似大模型（LLM）全参数微调所得的增量参数，从而达到使用更少显存占用的高效微调
LoRA的核心思想是 冻结预训练模型权重，将可训练的低秩矩阵分解注入到Transform（model）架构的每一层，
在训练过程中只更新这两个矩阵的参数，其他模型参数不变，从而减少在下游任务上的可训练参数量
在推理时，可直接将原预训练模型权重与训练好的LoRA权重合并，因此不存在额外开销
秩的选择一般为 8
    *   LoRA的训练过程：选择一个预训练大模型，Lory模型会选择一些目标层，配置LoRA参数 秩（r） 是LoRA的关键参数，
                        他决定了低秩矩阵A和B的维度 r越小参数越小，r越大参数越大
                        准备训练数据，训练数据一般是问答对，带有思考模式的模型，数据包含思维链，
                        前向传播，输入数据进入大模型，在冻结的基础模型层中计算权重相加缩放
                        计算损失，计算模型预测和真实值之间的误差
                        反向传播，LoRA的冻结的模型参数不会接收到任何梯度这也是显存和计算节省的核心
                        参数更新，优化器根据计算得到的梯度，只更新低秩矩阵A和B的参数，
                        模型合并，训练好的低秩举矩阵参数非常小，先加载原始模型，在加载训练好的模型权重，计算合并
                                得到完整的模型权重， 后续和使用普通模型的一样加载，
    *   为什么使用LoRA：因为其他微调增加了参数，改变了模型结构，把新的参数插到模型内部，
        导致模型在训练，推理的计算成本和内存增加
    *   容易遇到的问题：
            灾难性遗忘：解决方法：配比一些通用数据，通过使用稀疏性来减轻灾难性遗忘
            过拟合：

