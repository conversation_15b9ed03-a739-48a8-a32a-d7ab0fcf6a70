
HNSW
https://www.zhihu.com/search?type=content&q=hnsw

RAG:
    分为三个部分，1，数据准备 2，数据检索3，数据生成
    解决问题:时效性，专业性，大模型的幻觉问题

    文档的内容包括：某个领域的专业书籍，规章制度等，都可以。
    问答的格式包括：有PDF格式、图片格式、txt格式、word格式、Excel格式等等
    1.首先我们需要将数据进行清洗，去停用词，以及去掉一些反党反社会的语句，
    然后将处理好的数据进行切分，比如说，我们可以设置一下块儿与块儿之间交叉重叠的这个字数儿。这样能够一定程度上，
    缓解某一句话被切断的可能。这只能缓解，但是不能彻底解决某一句话被从当中切断的可能。还有一种切分的方
    法是按递归的方式去切分。一个文文档比较长，如果我们按照固定字数对问答进行切块，比如200个字一块，
    200 个字之后就切断，这样可能会导致语义不完整。最好的办法就是先按段落切，如果段落还比较长，
    就按句子切。如果句子还比较长的话呢，就按逗号儿去切啊，最后再按固定字数儿去切。
    这样做的目的是尽可能的在一个文本块里面，尽可能保持整段、整句的文本。进而缓解，
    一段相关的文字从中间切开的情况。因为一段完整的语义被切开后，被切开的两个部分语义都不完整。
    那么将文本小块儿转成向量之后，我们要把这个向量存起来。向量库一般使用：faiss，
    ChromaDB，Milvus(长期记忆，保存对话内容在磁盘上)。 存mysql 也可以，但是mysql对向量的检索比较慢。使用，向量库检索效
    率更高。

    2.将处理好的数据经过embedding-model转成向量，向量化模型：(输入是一个文档，输出是一个向量),
    常用的embedding-model有 BGE-base(512,1024), m3e-base(512, 768), BGE-M3(8192, 1024)，存入数据库中，
    数据库中的存储格式一般来说是以原文以及他的向量来进行存储的，用户问题转成的向量和文本块转成的向量，
    借助向量库进行相似性匹配用余弦相似度，找到与用户问题最相关的文本块。

    3.我们将用户输入的问题经过embedding-model转成向量，
    HNSW:向量数据库的索引方法
    先用hnsw(近似邻搜索)对其进行快速组排，做语义相似度检索返回top n 通过余弦相似度过滤相似度太低的数据,
    hnsw的工作原理是分成三个层，最底下那个层是包括所有的点，中间层点的数量是最底下层的十分之一，
    最顶层是中间层的十分之一，然后我们指定一个目标点，先在最顶层的节点中找到与目标点最相似的点，
    然后将找到的这个点用作于中间层的目标点，在中间层中找点与目标点最相似的点，
    再将这个点用作于最底层的目标点，找到与这个目标点相连的所有点，
    然后用余弦相似度找到与问题向量最相似的数据向量，然后返回向量的原文，
    加上重排序模型BGE-rerank 他的输入是问题，输出是问题相关性分数，然后进行排序，筛选TOP-N条数据，
    将这个返回的数据替换掉原来的提示词模版，模型经过这个替换后的提示词模版进行生成回复

RAG框架
'''
LangChain，LLamaIndex，dify，RAGAnything
'''

RAG 优化
分割，文档质量（语法检查），提示词，多路召回（mysql，nl2sql，neo4j），
混合检索（关键词检索，向量检索），评测


技术：
数据集： github上的数据集
角色信息：新闻记者
大语言模型 ：deepseek-R1 的 在线API接口
Embedding-model：Bge-M3(本地)

***重排序模型：Bge-rerank：
输入 -- 用户的问题（query），待排序的文档，
输出 -- 相关性分数：分数越高相关性越强
根据相关性分数进行排序，筛选top-n 作为大模型（LLM）的输入

RAG文本分块工具：将文本分割为带有重叠部分的块
短期记忆： redis 存储用户和模型的对话内容 我的设置是10条
长期记忆： Milvu 重叠分块的知识库,存储用户的问题和模型的回答

RAG评测：
人工评测，测试集按余弦相似度找到评分高