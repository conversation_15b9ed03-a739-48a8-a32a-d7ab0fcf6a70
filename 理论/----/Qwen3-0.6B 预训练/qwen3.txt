==========================
Qwen3-0.6B 微调训练技术报告
==========================

一、项目目标
------------
基于阿里 Qwen3-0.6B 模型，在小规模中文数据集上进行微调，快速适配下游任务场景，提升模型对定制数据的理解与生成效果。

二、核心技术方案
----------------
1️⃣ Huggingface Transformers 框架
2️⃣ Causal Language Modeling（因果语言模型）
3️⃣ 梯度累积模拟大 batch
4️⃣ 自动显存监控、进度条输出日志
5️⃣ 支持显存优化（gradient checkpoint、fp32 显存预留调整）
6️⃣ 数据格式统一为连续 token、固定 block_size 长度
7️⃣ 线性 warmup + 线性 decay 学习率策略

三、关键超参数配置
-----------------
| 参数名称                 | 数值     | 说明                    |
|-------------------------|--------|-------------------------|
| batch_size              | 4      | 单批次数据量             |
| gradient_accumulation    | 4      | 每累计 4 step 更新一次    |
| block_size              | 256    | 最长 token 序列长度       |
| learning_rate           | 5e-5   | 初始学习率                |
| weight_decay            | 0.01   | 权重衰减防止过拟合         |
| warmup_steps            | 10%    | 预热步数比例               |
| epochs                  | 3      | 总训练轮数                 |
| precision               | fp32   | 纯 float32 训练            |

四、数据流动逻辑
---------------
1️⃣ Dataset：MultiFormatDataset 负责按 block_size 切分、转 token
2️⃣ DataLoader：批次采样数据（shuffle=True）
3️⃣ Model：CausalLM，labels = inputs 左右 shift
4️⃣ Optimizer：AdamW + Scheduler（warmup+线性下降）
5️⃣ 梯度累积：4 step 汇总后统一更新
6️⃣ 显存监控：tqdm 实时 GPU 使用展示
7️⃣ 最终产物：保存 Huggingface 标准模型格式

五、输出示例日志
---------------
Epoch 1/3: 100%|██████████| 200/200 [00:20<00:00, 10.00it/s, Loss=1.2345, LR=5.00e-05, GPU=3.21G / 7.91G, Step=50]

六、最终产物结构
--------------
/root/autodl-tmp/Qwen3-0.6B/Qwen/Qwen3-0.6B-finetune
├── config.json
├── pytorch_model.bin
├── tokenizer_config.json
├── tokenizer.model
├── special_tokens_map.json

七、优化建议（未来版本可选项）
---------------------------
✅ 支持 FP16 / BF16 混合精度，节省显存
✅ 增加 wandb / tensorboard 日志
✅ 加入评估指标（Perplexity / BLEU）
✅ 增强 DDP 多卡训练能力
✅ 加入 EarlyStopping 自动停止
✅ 支持推理测试脚本

八、总结
------
该训练脚本可快速对通用模型进行定制小规模微调，训练流程稳定，显存利用清晰可控，适合小数据实验、定制语料调优、快速 SFT 场景。
