Transformer Encoder架构
多头自注意力（Multi-Head Self-Attention, MSA）
前馈网络
残差连接与层归一化

https://zhuanlan.zhihu.com/p/640013974


Patch Embedding（图像分块嵌入）：对原始图像进行切块处理，假设一个图像的大小为224*224
将图像切成一个个固定大小 16*16 ，每一个小方块就是一个 patch(块)
图像的 patch 数为 （224*224）/（16*16）=196个
得到 196 个[16,16,3]的patch，将这些pathc 送入展平，每个token的维度为 16*16*3 = 768
所以最后一共为 [196,768]

Patch Embedding的作用是将一个CV问题通过切块和展平转化为一个NLP问题。

Position Embedding：图像的每个patchc 和文本一样，也有先后顺序，这个层的作用就是
给每个token 添加位置信息，还需要添加一个 特殊字符 class token（添加在序列开头的特殊向量，
记录每个块的位置信息），所以最后得到的维度为【197,768】


Transformer Encoder（编码器堆叠）：将维度[197,768]的序列输入到标准的 Transform Encoder中
结构 LayerNorm -> MSA(多头自注意力机制) -> 残差链接 -> LayerNorm -> FFN(前馈神经网络) -> 残差链接 -> 输出
Transformer Encoder×L 是由 L 个相同结构的编码块堆叠而成，每个块包含
层归一化（稳定训练）
多头自注意力（建模全局依赖）
残差连接（保留原始信息）
前馈网络（增强非线性） 通过这种设计，ViT 实现了从局部特征到全局语义的渐进式学习，成为多模态任务的强大骨干网络。

MLP Head（分类头）:MLP Head的作用是用于最终的分类。
得到Transformer Encoder 的维度，先进过升维提取关键的向量，然后通过GELU激活函数去除冗余数据，压缩维度到1000
最后做softmax输出概率分布
