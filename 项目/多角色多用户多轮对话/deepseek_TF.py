# -*- coding: utf-8 -*-
# app.py - 后端服务器（带RAG功能）
import os
import json
import numpy as np
import redis
from flask import Flask, request, jsonify, render_template
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from openai import OpenAI
from flask_cors import CORS
import glob
import time
import re

app = Flask(__name__)
CORS(app)  # 启用跨域支持
app.config['JSON_AS_ASCII'] = False  # 允许中文

# 配置设置
CONFIG = {
    "model_type": "online",
    "online_api_base": "https://api-inference.modelscope.cn/v1/",
    "online_api_key": "56829c8b-e5db-4bd2-b9c1-35c354fffa1c",
    "online_model": "deepseek-ai/DeepSeek-R1-0528",
    "redis_host": "localhost",
    "redis_port": 6379,
    "redis_db": 0,
    "max_history": 10,
    "character_dir": "character_prompts",
    "knowledge_base_dir": "knowledge_base",
    "top_k": 3,  # 检索返回的相关文档数量
    "rag_threshold": 0.3  # 相似度阈值
}

# 确保目录存在
os.makedirs(CONFIG['character_dir'], exist_ok=True)
os.makedirs(CONFIG['knowledge_base_dir'], exist_ok=True)

# 初始化Redis
redis_client = redis.StrictRedis(
    host=CONFIG['redis_host'],
    port=CONFIG['redis_port'],
    db=CONFIG['redis_db'],
    decode_responses=True
)

# 初始化OpenAI客户端
online_client = OpenAI(
    base_url=CONFIG['online_api_base'],
    api_key=CONFIG['online_api_key']
)

# TF-IDF向量化器和知识库向量
tfidf_vectorizer = None
tfidf_matrix = None
knowledge_docs = []  # 知识文档列表
knowledge_index = {}  # 快速查找索引


# -------------------- RAG 功能增强 --------------------
def load_knowledge_base():
    """加载知识库并构建索引（使用TF-IDF）"""
    global tfidf_vectorizer, tfidf_matrix, knowledge_docs, knowledge_index
    knowledge_docs = []
    knowledge_index = {}

    # 读取所有知识文件
    pattern = os.path.join(CONFIG['knowledge_base_dir'], "*.txt")
    for file_path in glob.glob(pattern):
        filename = os.path.basename(file_path)
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()
            doc_info = {
                "content": content,
                "source": filename,
                "keywords": extract_keywords(content)
            }
            knowledge_docs.append(doc_info)
            base_name = os.path.splitext(filename)[0].lower()
            knowledge_index[base_name] = doc_info

    if not knowledge_docs:
        tfidf_vectorizer = None
        tfidf_matrix = None
        return

    # 使用TF-IDF向量化
    contents = [doc["content"] for doc in knowledge_docs]
    tfidf_vectorizer = TfidfVectorizer()
    tfidf_matrix = tfidf_vectorizer.fit_transform(contents)


def extract_keywords(text, top_n=10):
    """从文本中提取关键词"""
    words = re.findall(r'\b\w{3,}\b', text.lower())
    word_count = {}
    for word in words:
        if word not in ["的", "是", "在", "和", "有", "我", "你", "他", "她", "它", "了", "着", "过"]:
            word_count[word] = word_count.get(word, 0) + 1
    sorted_words = sorted(word_count.items(), key=lambda x: x[1], reverse=True)
    return [word for word, count in sorted_words[:top_n]]


def retrieve_documents(query, top_k=3):
    """检索相关文档（使用TF-IDF余弦相似度）"""
    global tfidf_vectorizer, tfidf_matrix, knowledge_docs

    if not knowledge_docs or tfidf_vectorizer is None:
        return []

    # 生成查询向量并计算余弦相似度
    query_vec = tfidf_vectorizer.transform([query])
    similarities = cosine_similarity(query_vec, tfidf_matrix)[0]
    doc_scores = list(zip(knowledge_docs, similarities))
    doc_scores.sort(key=lambda x: x[1], reverse=True)

    # 获取top_k结果
    results = []
    for i, (doc, score) in enumerate(doc_scores[:top_k]):
        if score > CONFIG['rag_threshold']:
            results.append({
                "content": doc["content"],
                "source": doc["source"],
                "score": float(score)
            })
    return results


# 初始化时加载知识库
load_knowledge_base()


# -------------------- 角色管理 --------------------
def get_character_prompt(character_id):
    """获取角色提示词"""
    char_path = os.path.join(CONFIG['character_dir'], f"{character_id}.txt")
    if os.path.exists(char_path):
        with open(char_path, 'r', encoding='utf-8') as f:
            return f.read().strip()
    return "你是一个乐于助人的助手。"


def save_character_prompt(character_id, prompt):
    """保存角色提示词"""
    char_path = os.path.join(CONFIG['character_dir'], f"{character_id}.txt")
    with open(char_path, 'w', encoding='utf-8') as f:
        f.write(prompt)


# -------------------- 对话历史管理 --------------------
def get_conversation_key(user_id, character_id):
    """生成对话存储键"""
    return f"conv:{user_id}:{character_id}"


def get_user_history(user_id, character_id):
    """从Redis获取用户对话历史"""
    key = get_conversation_key(user_id, character_id)
    history = redis_client.lrange(key, 0, CONFIG['max_history'] - 1)
    return [json.loads(item) for item in history]


def add_to_history(user_id, character_id, role, text):
    """添加新对话到Redis"""
    key = get_conversation_key(user_id, character_id)
    new_entry = json.dumps({"role": role, "content": text})
    redis_client.lpush(key, new_entry)
    redis_client.ltrim(key, 0, CONFIG['max_history'] - 1)


# -------------------- 响应生成 --------------------
def generate_response(user_id, character_id, user_input):
    """生成角色扮演回复（带RAG功能）"""
    character_prompt = get_character_prompt(character_id)
    history = get_user_history(user_id, character_id)
    add_to_history(user_id, character_id, "user", user_input)

    # 构建消息列表
    messages = [{"role": "system", "content": character_prompt}]
    for entry in reversed(history):
        messages.append({
            "role": "user" if entry['role'] == "user" else "assistant",
            "content": entry['content']
        })

    # RAG: 检索相关知识
    rag_context = ""
    retrieved = retrieve_documents(user_input, CONFIG['top_k'])
    if retrieved:
        context_info = "\n\n[相关背景知识]:\n"
        for i, doc in enumerate(retrieved):
            context_info += f"知识片段 {i + 1} (来自 {doc['source']}): {doc['content'][:150]}...\n"
        rag_context = context_info

    # 将知识添加到系统提示中
    if rag_context:
        messages[0]['content'] += rag_context

    # 添加当前用户输入
    messages.append({"role": "user", "content": user_input})

    # 调用API生成响应
    try:
        time.sleep(0.5)  # 添加延迟避免请求过快
        response = online_client.chat.completions.create(
            model=CONFIG['online_model'],
            messages=messages,
            max_tokens=300,
            temperature=0.7,
            stream=False
        )
        assistant_reply = response.choices[0].message.content.strip()
        add_to_history(user_id, character_id, "assistant", assistant_reply)
        return assistant_reply, rag_context if rag_context else ""

    except Exception as e:
        return f"生成回复时出错: {str(e)}", ""


# -------------------- API 路由 --------------------
@app.route('/')
def index():
    return render_template('index.html')


@app.route('/characters', methods=['GET'])
def list_characters():
    characters = []
    for file in os.listdir(CONFIG['character_dir']):
        if file.endswith('.txt'):
            char_id = os.path.splitext(file)[0]
            with open(os.path.join(CONFIG['character_dir'], file), 'r', encoding='utf-8') as f:
                prompt = f.read().strip()
            characters.append({
                "id": char_id,
                "name": char_id,
                "preview": prompt[:50] + "..." if len(prompt) > 50 else prompt
            })
    return jsonify({"characters": characters})


@app.route('/character', methods=['POST'])
def create_character():
    data = request.json
    character_id = data.get('id')
    prompt = data.get('prompt')

    if not character_id or not prompt:
        return jsonify({"error": "缺少角色ID或提示词"}), 400

    save_character_prompt(character_id, prompt)
    return jsonify({"status": "success", "character_id": character_id})


@app.route('/kb', methods=['POST'])
def upload_knowledge():
    """上传知识文档"""
    if 'file' not in request.files:
        return jsonify({"error": "未上传文件"}), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({"error": "未选择文件"}), 400

    if file and file.filename.endswith('.txt'):
        file_path = os.path.join(CONFIG['knowledge_base_dir'], file.filename)
        file.save(file_path)
        load_knowledge_base()  # 重新加载知识库
        return jsonify({"status": "success", "filename": file.filename})

    return jsonify({"error": "仅支持txt文件"}), 400


@app.route('/kb', methods=['GET'])
def list_knowledge():
    """列出知识库文件"""
    files = []
    for file in os.listdir(CONFIG['knowledge_base_dir']):
        if file.endswith('.txt'):
            file_path = os.path.join(CONFIG['knowledge_base_dir'], file)
            size = os.path.getsize(file_path)
            files.append({
                "name": file,
                "size": size
            })
    return jsonify({"files": files})


@app.route('/kb', methods=['DELETE'])
def delete_knowledge():
    """删除知识文档"""
    filename = request.args.get('filename')
    if not filename:
        return jsonify({"error": "未指定文件名"}), 400

    file_path = os.path.join(CONFIG['knowledge_base_dir'], filename)
    if os.path.exists(file_path) and filename.endswith('.txt'):
        os.remove(file_path)
        load_knowledge_base()  # 重新加载知识库
        return jsonify({"status": "success", "filename": filename})

    return jsonify({"error": "文件不存在或不是txt文件"}), 404


@app.route('/chat', methods=['POST'])
def chat():
    """处理聊天请求（返回上下文信息）"""
    data = request.json
    user_id = data.get('user_id', 'default_user')
    character_id = data.get('character_id', 'default_character')
    message = data.get('message', '')

    if not message:
        return jsonify({"error": "空消息"}), 400
    if not character_id:
        return jsonify({"error": "未指定角色"}), 400

    try:
        response, context = generate_response(user_id, character_id, message)
        return jsonify({"response": response, "context": context})
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route('/history', methods=['GET'])
def get_history():
    user_id = request.args.get('user_id', 'default_user')
    character_id = request.args.get('character_id', 'default_character')

    try:
        history = get_user_history(user_id, character_id)
        return jsonify({"history": list(reversed(history))})
    except Exception as e:
        return jsonify({"error": str(e)}), 500


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)