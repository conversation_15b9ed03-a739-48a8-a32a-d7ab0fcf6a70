<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>角色扮演聊天助手</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }

        :root {
            --primary-color: #8e44ad;
            --primary-dark: #6c3483;
            --secondary-color: #2980b9;
            --light-bg: #f9f7fa;
            --dark-bg: #2c3e50;
            --sidebar-bg: #1a1a2e;
            --chat-bg: #fff;
            --user-message: #e1bee7;
            --ai-message: #d1c4e9;
            --text-light: #f5f5f5;
            --text-dark: #333;
            --success: #27ae60;
            --error: #e74c3c;
            --warning: #f39c12;
        }

        body {
            background: var(--light-bg);
            color: var(--text-dark);
            display: flex;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 280px;
            background: var(--sidebar-bg);
            color: var(--text-light);
            padding: 20px;
            height: 100vh;
            position: fixed;
            overflow-y: auto;
            transition: transform 0.3s ease;
            box-shadow: 3px 0 15px rgba(0,0,0,0.2);
            z-index: 100;
        }

        .logo {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .logo i {
            font-size: 28px;
            color: var(--primary-color);
            margin-right: 12px;
        }

        .logo h1 {
            font-size: 22px;
            font-weight: 600;
            background: linear-gradient(to right, #c084fc, #8e44ad);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }

        .current-character {
            background: rgba(255,255,255,0.1);
            padding: 12px 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-size: 14px;
            display: flex;
            align-items: center;
        }

        .current-character i {
            margin-right: 10px;
            color: var(--primary-color);
        }

        .characters-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 25px 0 15px;
        }

        .characters-header h2 {
            font-size: 18px;
            font-weight: 500;
        }

        .add-character-btn {
            background: var(--primary-color);
            border: none;
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
        }

        .add-character-btn:hover {
            background: var(--primary-dark);
            transform: rotate(90deg);
        }

        #characterList {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .character-card {
            background: rgba(255,255,255,0.08);
            border-radius: 8px;
            padding: 12px 15px;
            cursor: pointer;
            transition: all 0.3s;
            border: 1px solid transparent;
        }

        .character-card:hover {
            background: rgba(255,255,255,0.15);
            border-color: rgba(142, 68, 173, 0.5);
        }

        .character-card.active {
            background: rgba(142, 68, 173, 0.2);
            border-color: var(--primary-color);
        }

        .character-name {
            font-weight: 600;
            font-size: 15px;
            color: var(--primary-color);
            margin-bottom: 4px;
        }

        .character-preview {
            font-size: 13px;
            opacity: 0.8;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .no-characters, .error {
            background: rgba(255,255,255,0.08);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            font-size: 14px;
        }

        .error {
            background: rgba(231, 76, 60, 0.15);
            color: #ff9e9e;
        }

        /* 主聊天区域 */
        .main-content {
            flex: 1;
            margin-left: 280px;
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        .chat-header {
            background: var(--primary-color);
            color: white;
            padding: 15px 25px;
            display: flex;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 10;
        }

        .chat-header h2 {
            font-weight: 500;
            font-size: 18px;
            flex: 1;
        }

        .menu-toggle {
            display: none;
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 5px;
            margin-right: 10px;
        }

        .chat-container {
            flex: 1;
            padding: 25px;
            overflow-y: auto;
            background: var(--chat-bg);
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .welcome-message {
            text-align: center;
            padding: 40px 20px;
            color: #777;
        }

        .welcome-message i {
            font-size: 60px;
            color: #e0d6eb;
            margin-bottom: 20px;
        }

        .welcome-message h3 {
            font-size: 24px;
            font-weight: 300;
            margin-bottom: 15px;
        }

        .welcome-message p {
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .message {
            padding: 15px 20px;
            border-radius: 15px;
            max-width: 80%;
            position: relative;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .user-message {
            background: var(--user-message);
            align-self: flex-end;
            border-bottom-right-radius: 5px;
        }

        .ai-message {
            background: var(--ai-message);
            align-self: flex-start;
            border-bottom-left-radius: 5px;
        }

        .message-role {
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 8px;
            color: var(--primary-dark);
        }

        .message-content {
            line-height: 1.5;
            white-space: pre-wrap;
            word-break: break-word;
        }

        .chat-input-area {
            background: white;
            padding: 20px;
            border-top: 1px solid #eee;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        #messageInput {
            flex: 1;
            padding: 14px 18px;
            border: 1px solid #ddd;
            border-radius: 50px;
            font-size: 15px;
            resize: none;
            height: 60px;
            max-height: 150px;
            transition: all 0.3s;
        }

        #messageInput:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(142, 68, 173, 0.1);
        }

        #sendBtn {
            background: var(--primary-color);
            color: white;
            border: none;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            font-size: 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
        }

        #sendBtn:hover {
            background: var(--primary-dark);
            transform: scale(1.05);
        }

        #sendBtn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        /* 打字动画 */
        .typing-indicator {
            background: var(--ai-message);
            padding: 15px 20px;
            border-radius: 15px;
            border-bottom-left-radius: 5px;
            align-self: flex-start;
            display: flex;
            gap: 8px;
        }

        .typing-dot {
            width: 10px;
            height: 10px;
            background: var(--primary-dark);
            border-radius: 50%;
            animation: bounce 1.5s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) {
            animation-delay: -0.32s;
        }

        .typing-dot:nth-child(2) {
            animation-delay: -0.16s;
        }

        @keyframes bounce {
            0%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            width: 90%;
            max-width: 500px;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            animation: modalFade 0.3s ease;
        }

        @keyframes modalFade {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .modal-header {
            background: var(--primary-color);
            color: white;
            padding: 20px;
        }

        .modal-header h2 {
            font-weight: 500;
            font-size: 20px;
        }

        .modal-body {
            padding: 25px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-dark);
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 15px;
            transition: border-color 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .modal-footer {
            padding: 15px 25px;
            background: #f9f9f9;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .btn {
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 15px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-dark);
        }

        .btn-outline {
            background: transparent;
            border: 1px solid #ddd;
            color: var(--text-dark);
        }

        .btn-outline:hover {
            background: #f5f5f5;
        }

        /* 响应式设计 */
        @media (max-width: 900px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .menu-toggle {
                display: block;
            }

            .message {
                max-width: 90%;
            }
        }

        @media (max-width: 480px) {
            .chat-container {
                padding: 15px;
            }

            .chat-input-area {
                padding: 15px;
            }

            #messageInput {
                padding: 12px 15px;
                height: 50px;
            }

            #sendBtn {
                width: 45px;
                height: 45px;
            }
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb {
            background: #c5b5d1;
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color);
        }

        .sidebar::-webkit-scrollbar-track {
            background: transparent;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255,255,255,0.3);
        }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar" id="sidebar">
        <div class="logo">
            <i class="fas fa-robot"></i>
            <h1>角色扮演聊天助手</h1>
        </div>

        <div class="current-character">
            <i class="fas fa-user-alt"></i>
            <span id="currentCharacter">请选择角色</span>
        </div>

        <div class="characters-header">
            <h2>角色列表</h2>
            <button id="addCharacterBtn" class="add-character-btn" title="添加新角色">
                <i class="fas fa-plus"></i>
            </button>
        </div>

        <div id="characterList">
            <!-- 角色卡片将通过JS动态加载 -->
            <div class="no-characters">加载角色中，请稍候...</div>
        </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
        <!-- 聊天头部 -->
        <div class="chat-header">
            <button class="menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <h2 id="chatTitle">角色扮演聊天助手</h2>
        </div>

        <!-- 聊天容器 -->
        <div class="chat-container" id="chatContainer">
            <div class="welcome-message">
                <i class="fas fa-comments"></i>
                <h3>欢迎使用角色扮演聊天助手</h3>
                <p>从左侧选择一个角色开始对话，或者创建新角色自定义交互体验。AI助手将根据角色设定进行回应，带来沉浸式的角色扮演体验。</p>
            </div>
        </div>

        <!-- 聊天输入区域 -->
        <div class="chat-input-area">
            <textarea
                id="messageInput"
                placeholder="输入消息..."
                disabled
                rows="1"
            ></textarea>
            <button id="sendBtn" disabled>
                <i class="fas fa-paper-plane"></i>
            </button>
        </div>
    </div>

    <!-- 添加角色模态框 -->
    <div class="modal" id="characterModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>创建新角色</h2>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="characterId">角色名称</label>
                    <input
                        type="text"
                        id="characterId"
                        class="form-control"
                        placeholder="例如：机器人助手、心理咨询师、历史人物..."
                    >
                </div>
                <div class="form-group">
                    <label for="characterPrompt">角色提示词</label>
                    <textarea
                        id="characterPrompt"
                        class="form-control"
                        rows="6"
                        placeholder="描述角色的背景、性格、说话方式等。例如：你是专业的心理咨询师，说话温和且富有同理心，帮助来访者解决心理困扰..."
                    ></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button id="cancelBtn" class="btn btn-outline">取消</button>
                <button id="saveCharacterBtn" class="btn btn-primary">保存角色</button>
            </div>
        </div>
    </div>

    <script>
        // DOM元素
        const characterList = document.getElementById('characterList');
        const chatContainer = document.getElementById('chatContainer');
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');
        const addCharacterBtn = document.getElementById('addCharacterBtn');
        const characterModal = document.getElementById('characterModal');
        const saveCharacterBtn = document.getElementById('saveCharacterBtn');
        const cancelBtn = document.getElementById('cancelBtn');
        const currentCharacter = document.getElementById('currentCharacter');
        const sidebar = document.getElementById('sidebar');

        // 全局变量
        let selectedCharacterId = null;
        let userId = 'user_' + Math.random().toString(36).substr(2, 9); // 生成随机用户ID
        let isGenerating = false;

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            // 加载角色列表
            loadCharacters();

            // 添加事件监听器
            addCharacterBtn.addEventListener('click', () => {
                document.getElementById('characterId').value = '';
                document.getElementById('characterPrompt').value = '';
                characterModal.style.display = 'flex';
            });

            saveCharacterBtn.addEventListener('click', saveCharacter);
            cancelBtn.addEventListener('click', () => {
                characterModal.style.display = 'none';
            });

            sendBtn.addEventListener('click', sendMessage);

            messageInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    if (!isGenerating) {
                        sendMessage();
                    }
                }
            });

            // 移动端侧边栏切换
            document.querySelector('.menu-toggle').addEventListener('click', () => {
                sidebar.classList.toggle('active');
            });

            // 点击模态框外部关闭
            characterModal.addEventListener('click', (e) => {
                if (e.target === characterModal) {
                    characterModal.style.display = 'none';
                }
            });

            // 初始禁用输入框
            messageInput.disabled = true;
            sendBtn.disabled = true;
        });

        // 加载角色列表
        async function loadCharacters() {
            try {
                const response = await fetch('/characters');

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`服务器错误: ${response.status} ${errorText}`);
                }

                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    const text = await response.text();
                    throw new Error(`无效的响应类型: ${contentType}`);
                }

                const data = await response.json();

                characterList.innerHTML = '';

                if (data.characters && data.characters.length > 0) {
                    data.characters.forEach(character => {
                        const characterElement = document.createElement('div');
                        characterElement.className = 'character-card';
                        characterElement.innerHTML = `
                            <div class="character-name">${character.id}</div>
                            <div class="character-preview">${character.preview}</div>
                        `;

                        characterElement.addEventListener('click', () => {
                            selectCharacter(character.id);

                            // 移除所有角色卡片的active类
                            document.querySelectorAll('.character-card').forEach(card => {
                                card.classList.remove('active');
                            });

                            // 为当前选择的角色卡片添加active类
                            characterElement.classList.add('active');

                            // 移动端选择角色后关闭侧边栏
                            if (window.innerWidth <= 900) {
                                sidebar.classList.remove('active');
                            }
                        });

                        characterList.appendChild(characterElement);
                    });
                } else {
                    characterList.innerHTML = '<div class="no-characters">暂无角色，请添加新角色开始对话。</div>';
                }
            } catch (error) {
                console.error('加载角色失败:', error);
                characterList.innerHTML = `<div class="error">无法加载角色列表: ${error.message}</div>`;
            }
        }

        // 选择角色
        function selectCharacter(characterId) {
            selectedCharacterId = characterId;
            currentCharacter.textContent = `当前角色: ${characterId}`;
            messageInput.disabled = false;
            sendBtn.disabled = false;
            messageInput.focus();

            // 加载该角色的历史记录
            loadChatHistory(characterId);
        }

        // 保存新角色
        async function saveCharacter() {
            const characterId = document.getElementById('characterId').value.trim();
            const prompt = document.getElementById('characterPrompt').value.trim();

            if (!characterId || !prompt) {
                alert('请填写角色名称和提示词');
                return;
            }

            try {
                const response = await fetch('/character', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        id: characterId,
                        prompt: prompt
                    })
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`服务器错误: ${response.status} ${errorText}`);
                }

                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    const text = await response.text();
                    throw new Error(`无效的响应类型: ${contentType}`);
                }

                const data = await response.json();

                if (data.status === "success") {
                    characterModal.style.display = 'none';
                    alert(`角色 "${characterId}" 创建成功！`);
                    loadCharacters();
                    selectCharacter(characterId);
                } else {
                    throw new Error(data.error || '创建角色失败，请重试');
                }
            } catch (error) {
                console.error('创建角色失败:', error);
                alert(`错误: ${error.message}`);
            }
        }

        // 加载聊天历史
        async function loadChatHistory(characterId) {
            try {
                const response = await fetch(`/history?user_id=${userId}&character_id=${characterId}`);

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`服务器错误: ${response.status} ${errorText}`);
                }

                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    const text = await response.text();
                    throw new Error(`无效的响应类型: ${contentType}`);
                }

                const data = await response.json();

                chatContainer.innerHTML = '';

                if (data.history && data.history.length > 0) {
                    data.history.forEach(message => {
                        addMessageToChat(message.role, message.content);
                    });
                } else {
                    chatContainer.innerHTML = `
                        <div class="welcome-message">
                            <i class="fas fa-user-circle"></i>
                            <h3>开始与 ${characterId} 对话</h3>
                            <p>这是一个新的对话，请发送第一条消息开始与 ${characterId} 的互动。</p>
                        </div>
                    `;
                }

                // 滚动到底部
                scrollToBottom();
            } catch (error) {
                console.error('加载聊天历史失败:', error);
                addMessageToChat('system', `加载聊天历史失败: ${error.message}`);
            }
        }

        // 发送消息
        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message || !selectedCharacterId || isGenerating) return;

            // 添加用户消息到聊天界面
            addMessageToChat('user', message);
            messageInput.value = '';

            // 显示打字动画
            showTypingIndicator();

            try {
                isGenerating = true;
                sendBtn.disabled = true;

                const response = await fetch('/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        user_id: userId,
                        character_id: selectedCharacterId,
                        message: message
                    })
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`服务器错误: ${response.status} ${errorText}`);
                }

                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    const text = await response.text();
                    throw new Error(`无效的响应类型: ${contentType}`);
                }

                const data = await response.json();

                // 移除打字动画
                hideTypingIndicator();

                if (data.response) {
                    // 添加AI回复到聊天界面
                    addMessageToChat('assistant', data.response);
                } else if (data.error) {
                    throw new Error(data.error);
                } else {
                    throw new Error('服务器返回了无效的响应');
                }
            } catch (error) {
                console.error('发送消息失败:', error);
                hideTypingIndicator();
                addMessageToChat('assistant', `⚠️ 发送消息出错: ${error.message}`);
            } finally {
                isGenerating = false;
                sendBtn.disabled = false;
                messageInput.focus();
            }
        }

        // 添加消息到聊天界面
        function addMessageToChat(role, content) {
            // 如果当前显示的是欢迎消息，则清除它
            if (chatContainer.querySelector('.welcome-message')) {
                chatContainer.innerHTML = '';
            }

            const messageElement = document.createElement('div');
            messageElement.className = `message ${role === 'user' ? 'user-message' : 'ai-message'}`;

            // 安全地转义HTML特殊字符，防止XSS攻击
            const escapedContent = content
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#039;');

            // 替换换行为<br>标签
            const formattedContent = escapedContent.replace(/\n/g, '<br>');

            messageElement.innerHTML = `
                <div class="message-role">${role === 'user' ? '你' : selectedCharacterId || '助手'}</div>
                <div class="message-content">${formattedContent}</div>
            `;

            chatContainer.appendChild(messageElement);
            scrollToBottom();
        }

        // 显示打字动画
        function showTypingIndicator() {
            // 如果当前显示的是欢迎消息，则清除它
            if (chatContainer.querySelector('.welcome-message')) {
                chatContainer.innerHTML = '';
            }

            const typingElement = document.createElement('div');
            typingElement.className = 'typing-indicator';
            typingElement.id = 'typingIndicator';
            typingElement.innerHTML = `
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            `;

            chatContainer.appendChild(typingElement);
            scrollToBottom();
        }

        // 隐藏打字动画
        function hideTypingIndicator() {
            const typingIndicator = document.getElementById('typingIndicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }

        // 滚动到底部
        function scrollToBottom() {
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }
    </script>
</body>
</html>