# -*- coding: utf-8 -*-
# 优化版后端服务器（集成重叠块嵌入的RAG）
import os
import json
import numpy as np
import torch
import redis
from flask import Flask, request, jsonify, render_template
from sklearn.metrics.pairwise import cosine_similarity
from openai import OpenAI
from flask_cors import CORS
import glob
import time
import re
from typing import List, Dict, Any, Tu<PERSON>
from transformers import AutoModel, AutoTokenizer
from pymilvus import connections, Collection, FieldSchema, CollectionSchema, DataType, utility

app = Flask(__name__)
CORS(app)  # 启用跨域支持
app.config['JSON_AS_ASCII'] = False  # 允许中文

# 配置设置（添加分块参数）
CONFIG = {
    "model_type": "online",
    "online_api_base": "https://api-inference.modelscope.cn/v1/",
    "online_api_key": "56829c8b-e5db-4bd2-b9c1-35c354fffa1c",
    "online_model": "deepseek-ai/DeepSeek-R1-0528",
    "bge_model_path": r"C:\Users\<USER>\.cache\modelscope\hub\models\BAAI\bge-m3",  # 本地BGE-M3模型路径
    "redis_host": "localhost",
    "redis_port": 6379,
    "redis_db": 0,
    "max_history": 10,  # Redis中保存的短期对话记录数量
    "character_dir": "character_prompts",
    "knowledge_base_dir": "knowledge_base",
    "top_k": 3,  # 知识检索返回的相关文档数量
    "history_top_k": 5,  # 历史记录检索数量
    "rag_threshold": 0.3,  # 相似度阈值
    "bge_batch_size": 8,  # 批量处理文档的大小
    "milvus_host": "localhost",  # Milvus配置
    "milvus_port": "19530",
    "knowledge_collection": "ai_knowledge_base",  # Milvus知识库集合名称
    "history_collection": "chat_history",  # Milvus历史记录集合名称
    "vector_dim": 1024,  # BGE-M3向量维度
    "chunk_size": 500,  # 文本块大小（字符数）
    "chunk_overlap": 100  # 块间重叠字符数
}

# 确保目录存在
os.makedirs(CONFIG['character_dir'], exist_ok=True)
os.makedirs(CONFIG['knowledge_base_dir'], exist_ok=True)

# 初始化Redis
redis_client = redis.StrictRedis(
    host=CONFIG['redis_host'],
    port=CONFIG['redis_port'],
    db=CONFIG['redis_db'],
    decode_responses=True
)

# 初始化OpenAI客户端
online_client = OpenAI(
    base_url=CONFIG['online_api_base'],
    api_key=CONFIG['online_api_key']
)


# -------------------- Milvus 初始化 --------------------
def initialize_milvus():
    """初始化Milvus连接并创建集合"""
    try:
        app.logger.info(f"尝试连接Milvus: {CONFIG['milvus_host']}:{CONFIG['milvus_port']}")
        connections.connect(
            host=CONFIG['milvus_host'],
            port=CONFIG['milvus_port']
        )
        app.logger.info("Milvus连接成功")

        # 创建知识库集合 (如果不存在)
        if not utility.has_collection(CONFIG['knowledge_collection']):
            # 定义字段
            fields = [
                FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
                FieldSchema(name="source", dtype=DataType.VARCHAR, max_length=200),
                FieldSchema(name="content", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=CONFIG['vector_dim'])
            ]

            # 创建集合
            schema = CollectionSchema(fields, description="AI知识库")
            knowledge_collection = Collection(CONFIG['knowledge_collection'], schema)

            # 创建索引
            index_params = {
                "index_type": "IVF_FLAT",
                "metric_type": "COSINE",
                "params": {"nlist": 128}
            }
            knowledge_collection.create_index("embedding", index_params)
            app.logger.info(f"创建知识库集合: {CONFIG['knowledge_collection']}")

        # 创建历史记录集合 (如果不存在)
        if not utility.has_collection(CONFIG['history_collection']):
            fields = [
                FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
                FieldSchema(name="user_id", dtype=DataType.VARCHAR, max_length=64),
                FieldSchema(name="character_id", dtype=DataType.VARCHAR, max_length=64),
                FieldSchema(name="role", dtype=DataType.VARCHAR, max_length=10),
                FieldSchema(name="content", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="timestamp", dtype=DataType.INT64),
                FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=CONFIG['vector_dim'])
            ]

            schema = CollectionSchema(fields, description="聊天历史记录")
            history_collection = Collection(CONFIG['history_collection'], schema)

            # 创建索引
            index_params = {
                "index_type": "IVF_FLAT",
                "metric_type": "COSINE",
                "params": {"nlist": 128}
            }
            history_collection.create_index("embedding", index_params)
            app.logger.info(f"创建历史记录集合: {CONFIG['history_collection']}")

        return True
    except Exception as e:
        app.logger.error(f"Milvus初始化失败: {str(e)}")
        return False


# -------------------- RAG 功能增强 --------------------
# 初始化BGE-M3模型和tokenizer
bge_tokenizer = None
bge_model = None


def initialize_bge_model():
    """初始化BGE-M3模型"""
    global bge_tokenizer, bge_model
    try:
        app.logger.info("开始加载BGE-M3模型...")
        start_time = time.time()

        # 使用GPU如果可用，否则使用CPU
        device = "cuda" if torch.cuda.is_available() else "cpu"
        app.logger.info(f"使用设备: {device}")

        # 加载tokenizer和模型
        bge_tokenizer = AutoTokenizer.from_pretrained(CONFIG['bge_model_path'])
        bge_model = AutoModel.from_pretrained(CONFIG['bge_model_path']).to(device)
        bge_model.eval()  # 设为评估模式

        load_time = time.time() - start_time
        app.logger.info(f"BGE-M3模型加载完成，耗时: {load_time:.2f}秒")
        return True
    except Exception as e:
        app.logger.error(f"模型加载失败: {str(e)}")
        return False


def get_embeddings(texts: List[str]) -> List[List[float]]:
    """使用本地加载的BGE-M3模型获取文本嵌入向量"""
    global bge_tokenizer, bge_model
    if bge_model is None:
        app.logger.error("BGE-M3模型未初始化")
        return [[0.0] * CONFIG['vector_dim'] for _ in texts]  # 后备方案

    try:
        # Tokenize输入文本
        inputs = bge_tokenizer(
            texts,
            padding=True,
            truncation=True,
            return_tensors='pt',
            max_length=512
        ).to(bge_model.device)

        # 生成嵌入向量
        with torch.no_grad():
            outputs = bge_model(**inputs)
            embeddings = outputs.last_hidden_state[:, 0]

        # 归一化嵌入向量（提高余弦相似度计算效果）
        embeddings = torch.nn.functional.normalize(embeddings, p=2, dim=1)

        # 转换为列表格式
        return embeddings.cpu().numpy().tolist()
    except Exception as e:
        app.logger.error(f"嵌入生成失败: {str(e)}")
        return [[0.0] * CONFIG['vector_dim'] for _ in texts]  # 后备方案


def extract_keywords(text: str, top_n: int = 10) -> List[str]:
    """从文本中提取关键词（优化版）"""
    words = re.findall(r'\b\w{3,}\b', text.lower())
    word_count = {}
    for word in words:
        if word not in ["的", "是", "在", "和", "有", "我", "你", "他", "她", "它", "了", "着", "过", "就", "也", "不",
                        "都"]:
            word_count[word] = word_count.get(word, 0) + 1
    sorted_words = sorted(word_count.items(), key=lambda x: x[1], reverse=True)
    return [word for word, count in sorted_words[:top_n]]


# -------------------- 文本分块工具函数 --------------------
def split_text_with_overlap(text: str, chunk_size: int, overlap: int) -> List[str]:
    """
    将文本分割为带有重叠部分的块
    """
    if not text:
        return []

    chunks = []
    start = 0
    total_length = len(text)

    while start < total_length:
        # 计算当前块的结束位置
        end = min(start + chunk_size, total_length)
        chunks.append(text[start:end])

        # 计算下一个块的起始位置（考虑重叠）
        if end == total_length:
            break
        start += chunk_size - overlap

    return chunks


# -------------------- 改进的知识库加载函数 --------------------
def load_knowledge_to_milvus():
    """加载知识库到Milvus向量数据库（带分块处理）"""
    # 检查集合是否存在
    if not utility.has_collection(CONFIG['knowledge_collection']):
        app.logger.error(f"知识库集合 {CONFIG['knowledge_collection']} 不存在")
        return False

    try:
        knowledge_coll = Collection(CONFIG['knowledge_collection'])
        knowledge_coll.load()

        # 读取所有知识文件
        pattern = os.path.join(CONFIG['knowledge_base_dir'], "*.txt")
        file_paths = glob.glob(pattern)

        if not file_paths:
            app.logger.info("知识库目录为空")
            return True

        # 准备批量插入数据
        sources = []
        contents = []
        embeddings = []

        app.logger.info(f"开始加载 {len(file_paths)} 个知识文档到Milvus...")

        for file_path in file_paths:
            filename = os.path.basename(file_path)
            with open(file_path, 'r', encoding='utf-8') as f:
                full_text = f.read().strip()

            # 分块处理文本
            chunks = split_text_with_overlap(
                full_text,
                chunk_size=CONFIG['chunk_size'],
                overlap=CONFIG['chunk_overlap']
            )

            # 为每个块创建元数据
            for chunk_idx, chunk_text in enumerate(chunks):
                # 添加块标识信息
                chunk_source = f"{filename}__chunk_{chunk_idx + 1}"
                sources.append(chunk_source)
                contents.append(chunk_text)

        app.logger.info(f"文档分块完成，共生成 {len(contents)} 个文本块")

        # 批量生成嵌入向量
        batch_size = CONFIG['bge_batch_size']
        app.logger.info(f"开始生成嵌入向量（批量大小: {batch_size}）...")
        for i in range(0, len(contents), batch_size):
            batch_texts = contents[i:i + batch_size]
            batch_embeddings = get_embeddings(batch_texts)
            embeddings.extend(batch_embeddings)

        # 插入Milvus
        entities = [sources, contents, embeddings]
        insert_result = knowledge_coll.insert(entities)

        app.logger.info(f"知识库加载完成，插入 {len(insert_result.primary_keys)} 条记录")
        return True
    except Exception as e:
        app.logger.error(f"知识库加载到Milvus失败: {str(e)}")
        return False


# -------------------- 优化文档检索函数 --------------------
def retrieve_documents(query: str, top_k: int = None) -> List[Dict[str, Any]]:
    """从Milvus知识库检索相关文档（带块聚合）"""
    if top_k is None:
        top_k = CONFIG['top_k']

    try:
        knowledge_coll = Collection(CONFIG['knowledge_collection'])
        knowledge_coll.load()

        # 获取查询的嵌入向量
        query_embedding = get_embeddings([query])[0]

        # 相似度搜索（扩大检索范围以获取更多相关块）
        search_params = {"metric_type": "COSINE", "params": {"nprobe": 128}}
        results = knowledge_coll.search(
            [query_embedding],
            anns_field="embedding",
            param=search_params,
            limit=top_k * 5,  # 扩大召回范围
            output_fields=["source", "content"]
        )

        # 处理结果：合并同一文档的多个块
        doc_map = {}
        for hit in results[0]:
            if hit.score < CONFIG['rag_threshold']:
                continue

            # 提取原始文件名（移除块标识）
            source = hit.entity.get("source")
            if '__chunk_' not in source:
                # 兼容旧格式数据
                raw_filename = source
                chunk_content = hit.entity.get("content")
            else:
                raw_filename = source.split("__chunk_")[0]
                chunk_content = hit.entity.get("content")

            # 合并文档内容
            if raw_filename not in doc_map:
                doc_map[raw_filename] = {
                    "source": raw_filename,
                    "content": chunk_content,
                    "best_score": hit.score,
                    "chunks": [chunk_content]
                }
            else:
                doc_map[raw_filename]["chunks"].append(chunk_content)
                doc_map[raw_filename]["best_score"] = max(
                    doc_map[raw_filename]["best_score"],
                    hit.score
                )

        # 重构文档内容（合并所有相关块）
        retrieved_docs = []
        for doc in doc_map.values():
            # 去除重叠部分的重复内容
            merged_content = ""
            last_chunk = ""
            for chunk in doc["chunks"]:
                # 跳过与前一块重叠的部分
                start_idx = len(last_chunk) - CONFIG['chunk_overlap']
                if start_idx > 0 and chunk.startswith(last_chunk[:CONFIG['chunk_overlap']]):
                    chunk = chunk[CONFIG['chunk_overlap']:]
                merged_content += chunk
                last_chunk = chunk

            # 保留原始文档内容长度（仅显示摘要）
            display_content = merged_content[:500] + "..." if len(merged_content) > 500 else merged_content
            retrieved_docs.append({
                "source": doc["source"],
                "content": display_content,
                "score": doc["best_score"],
                "full_content": merged_content  # 保留完整内容供后续使用
            })

        # 按分数排序并返回top_k结果
        retrieved_docs.sort(key=lambda x: x["score"], reverse=True)
        return retrieved_docs[:top_k]
    except Exception as e:
        app.logger.error(f"知识检索失败: {str(e)}")
        return []


# -------------------- Milvus 历史记录操作 --------------------
def save_history_to_milvus(user_id: str, character_id: str, role: str, text: str) -> bool:
    """保存对话历史到Milvus向量数据库"""
    try:
        history_coll = Collection(CONFIG['history_collection'])
        history_coll.load()

        # 生成文本嵌入
        embedding = get_embeddings([text])[0]
        timestamp = int(time.time() * 1000)  # 毫秒时间戳

        # 插入历史记录
        entities = [
            [user_id],
            [character_id],
            [role],
            [text],
            [timestamp],
            [embedding]
        ]
        history_coll.insert(entities)
        return True
    except Exception as e:
        app.logger.error(f"历史记录保存失败: {str(e)}")
        return False


def retrieve_history(query: str, user_id: str, character_id: str, top_k: int = None) -> List[Dict[str, Any]]:
    """从Milvus检索相关的历史对话"""
    if top_k is None:
        top_k = CONFIG['history_top_k']

    try:
        history_coll = Collection(CONFIG['history_collection'])
        history_coll.load()

        # 获取查询的嵌入向量
        query_embedding = get_embeddings([query])[0]

        # 构建过滤表达式
        expr = f'user_id == "{user_id}" and character_id == "{character_id}"'

        # 相似度搜索
        search_params = {"metric_type": "COSINE", "params": {"nprobe": 128}}
        results = history_coll.search(
            [query_embedding],
            anns_field="embedding",
            expr=expr,
            param=search_params,
            limit=top_k,
            output_fields=["role", "content", "timestamp"]
        )

        # 处理结果
        retrieved_history = []
        for hit in results[0]:
            history_entry = {
                "role": hit.entity.get("role"),
                "content": hit.entity.get("content")[:200] + "..." if len(
                    hit.entity.get("content")) > 200 else hit.entity.get("content"),
                "timestamp": hit.entity.get("timestamp"),
                "score": hit.score
            }
            retrieved_history.append(history_entry)

        # 按时间戳排序（从旧到新）
        retrieved_history.sort(key=lambda x: x["timestamp"])
        return retrieved_history
    except Exception as e:
        app.logger.error(f"历史记录检索失败: {str(e)}")
        return []


# -------------------- 角色管理 --------------------
def get_character_prompt(character_id: str) -> str:
    """获取角色提示词"""
    char_path = os.path.join(CONFIG['character_dir'], f"{character_id}.txt")
    if os.path.exists(char_path):
        with open(char_path, 'r', encoding='utf-8') as f:
            return f.read().strip()
    return "你是一个乐于助人的助手。"


def save_character_prompt(character_id: str, prompt: str) -> bool:
    """保存角色提示词"""
    char_path = os.path.join(CONFIG['character_dir'], f"{character_id}.txt")
    try:
        with open(char_path, 'w', encoding='utf-8') as f:
            f.write(prompt)
        return True
    except Exception as e:
        app.logger.error(f"保存角色提示词失败: {str(e)}")
        return False


# -------------------- Redis 对话历史管理 --------------------
def get_conversation_key(user_id: str, character_id: str) -> str:
    """生成对话存储键"""
    return f"conv:{user_id}:{character_id}"


def get_user_history(user_id: str, character_id: str) -> List[Dict[str, str]]:
    """从Redis获取用户短期对话历史"""
    key = get_conversation_key(user_id, character_id)
    history = redis_client.lrange(key, 0, CONFIG['max_history'] - 1)
    return [json.loads(item) for item in history] if history else []


def add_to_history(user_id: str, character_id: str, role: str, text: str) -> None:
    """添加新对话到Redis短期历史"""
    key = get_conversation_key(user_id, character_id)
    new_entry = json.dumps({"role": role, "content": text})
    redis_client.lpush(key, new_entry)
    redis_client.ltrim(key, 0, CONFIG['max_history'] - 1)


# -------------------- 响应生成 --------------------
def generate_response(user_id: str, character_id: str, user_input: str) -> Tuple[str, str]:
    """生成角色扮演回复（带RAG和历史检索功能）"""
    character_prompt = get_character_prompt(character_id)
    history = get_user_history(user_id, character_id)
    add_to_history(user_id, character_id, "user", user_input)

    # 构建消息列表
    messages = [{"role": "system", "content": character_prompt}]
    for entry in reversed(history):
        messages.append({
            "role": "user" if entry['role'] == "user" else "assistant",
            "content": entry['content']
        })

    context_info = ""  # 记录所有上下文信息

    # RAG: 检索相关知识
    retrieved_knowledge = retrieve_documents(user_input)
    if retrieved_knowledge:
        knowledge_context = "\n\n[相关背景知识]:\n"
        for i, doc in enumerate(retrieved_knowledge):
            knowledge_context += f"知识片段 {i + 1} (来自 {doc['source']}): {doc['content']}\n"
        context_info += knowledge_context

    # 检索相关历史对话
    retrieved_history = retrieve_history(user_input, user_id, character_id)
    if retrieved_history:
        history_context = "\n\n[相关历史对话]:\n"
        for i, item in enumerate(retrieved_history):
            speaker = "用户" if item['role'] == "user" else "AI助手"
            history_context += f"{speaker}: {item['content']}\n"
        context_info += history_context

    # 将上下文添加到系统提示中
    if context_info:
        messages[0]['content'] += context_info

    # 添加当前用户输入
    messages.append({"role": "user", "content": user_input})

    # 调用API生成响应
    try:
        time.sleep(0.5)  # 添加延迟避免请求过快
        response = online_client.chat.completions.create(
            model=CONFIG['online_model'],
            messages=messages,
            max_tokens=500,
            temperature=0.7,
            stream=False
        )
        assistant_reply = response.choices[0].message.content.strip()

        # 保存到短期历史
        add_to_history(user_id, character_id, "assistant", assistant_reply)

        # 保存到长期历史 (Milvus)
        save_history_to_milvus(user_id, character_id, "user", user_input)
        save_history_to_milvus(user_id, character_id, "assistant", assistant_reply)

        return assistant_reply, context_info

    except Exception as e:
        error_msg = f"生成回复时出错: {str(e)}"
        app.logger.error(error_msg)
        return error_msg, ""


# -------------------- API 路由 --------------------
@app.route('/')
def index():
    return render_template('index.html')


@app.route('/characters', methods=['GET'])
def list_characters():
    characters = []
    for file in os.listdir(CONFIG['character_dir']):
        if file.endswith('.txt'):
            char_id = os.path.splitext(file)[0]
            with open(os.path.join(CONFIG['character_dir'], file), 'r', encoding='utf-8') as f:
                prompt = f.read().strip()
            characters.append({
                "id": char_id,
                "name": char_id,
                "preview": prompt[:50] + "..." if len(prompt) > 50 else prompt
            })
    return jsonify({"characters": characters})


@app.route('/character', methods=['POST'])
def create_character():
    data = request.json
    character_id = data.get('id')
    prompt = data.get('prompt')

    if not character_id or not prompt:
        return jsonify({"error": "缺少角色ID或提示词"}), 400

    if save_character_prompt(character_id, prompt):
        return jsonify({"status": "success", "character_id": character_id})
    return jsonify({"error": "角色创建失败"}), 500


@app.route('/kb', methods=['POST'])
def upload_knowledge():
    """上传知识文档到Milvus"""
    if 'file' not in request.files:
        return jsonify({"error": "未上传文件"}), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({"error": "未选择文件"}), 400

    if file and file.filename.endswith('.txt'):
        # 保存文件到知识库目录
        file_path = os.path.join(CONFIG['knowledge_base_dir'], file.filename)
        file.save(file_path)

        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()

        # 分块处理文档
        chunks = split_text_with_overlap(
            content,
            chunk_size=CONFIG['chunk_size'],
            overlap=CONFIG['chunk_overlap']
        )

        # 插入到Milvus
        try:
            knowledge_coll = Collection(CONFIG['knowledge_collection'])
            knowledge_coll.load()

            sources = []
            contents = []
            embeddings = []

            # 为每个块生成元数据和嵌入
            for idx, chunk in enumerate(chunks):
                source = f"{file.filename}__chunk_{idx + 1}"
                sources.append(source)
                contents.append(chunk)

            # 批量生成嵌入
            batch_embeddings = get_embeddings(contents)
            embeddings.extend(batch_embeddings)

            # 插入到Milvus
            entities = [sources, contents, embeddings]
            insert_result = knowledge_coll.insert(entities)

            if insert_result.primary_keys:
                app.logger.info(f"上传知识成功: {file.filename}, 分块数: {len(chunks)}")
                return jsonify({"status": "success", "filename": file.filename, "chunks": len(chunks)})
            return jsonify({"error": "插入知识库失败"}), 500
        except Exception as e:
            return jsonify({"error": f"知识库操作失败: {str(e)}"}), 500

    return jsonify({"error": "仅支持txt文件"}), 400


@app.route('/kb', methods=['GET'])
def list_knowledge():
    """列出知识库文件（显示分块信息）"""
    files = []
    for file in os.listdir(CONFIG['knowledge_base_dir']):
        if file.endswith('.txt'):
            file_path = os.path.join(CONFIG['knowledge_base_dir'], file)
            size = os.path.getsize(file_path)

            # 估计分块数量
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            chunk_count = len(split_text_with_overlap(
                content,
                CONFIG['chunk_size'],
                CONFIG['chunk_overlap']
            ))

            files.append({
                "name": file,
                "size": size,
                "chunks": chunk_count
            })
    return jsonify({"files": files})


@app.route('/kb', methods=['DELETE'])
def delete_knowledge():
    """删除知识文档（处理分块记录）"""
    filename = request.args.get('filename')
    if not filename:
        return jsonify({"error": "未指定文件名"}), 400

    # 从本地文件系统删除
    file_path = os.path.join(CONFIG['knowledge_base_dir'], filename)
    if os.path.exists(file_path) and filename.endswith('.txt'):
        os.remove(file_path)

    # 从Milvus删除所有相关块 (根据source字段前缀)
    try:
        knowledge_coll = Collection(CONFIG['knowledge_collection'])
        knowledge_coll.load()

        # 构建删除表达式（匹配文件名开头的所有块）
        expr = f'source like "{filename}%"'
        delete_result = knowledge_coll.delete(expr)

        return jsonify({
            "status": "success",
            "filename": filename,
            "deleted_count": delete_result.delete_count
        })
    except Exception as e:
        return jsonify({"error": f"知识库删除失败: {str(e)}"}), 500


@app.route('/chat', methods=['POST'])
def chat():
    """处理聊天请求（返回上下文信息）"""
    data = request.json
    user_id = data.get('user_id', 'default_user')
    character_id = data.get('character_id', 'default_character')
    message = data.get('message', '')

    if not message:
        return jsonify({"error": "空消息"}), 400
    if not character_id:
        return jsonify({"error": "未指定角色"}), 400

    try:
        response, context = generate_response(user_id, character_id, message)
        return jsonify({"response": response, "context": context})
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route('/history', methods=['GET'])
def get_history():
    """获取短期对话历史（来自Redis）"""
    user_id = request.args.get('user_id', 'default_user')
    character_id = request.args.get('character_id', 'default_character')

    try:
        history = get_user_history(user_id, character_id)
        return jsonify({"history": list(reversed(history))})
    except Exception as e:
        return jsonify({"error": str(e)}), 500


# -------------------- 初始化 --------------------
def initialize_system():
    """初始化系统组件"""
    success = True

    # 1. 初始化嵌入模型
    if not initialize_bge_model():
        app.logger.error("BGE-M3模型初始化失败，RAG功能受限")
        success = False

    # 2. 初始化Milvus
    if not initialize_milvus():
        app.logger.error("Milvus初始化失败，长期存储功能受限")
        success = False

    # 3. 加载知识库到Milvus
    if success:
        if not load_knowledge_to_milvus():
            app.logger.error("知识库加载失败")
            success = False

    return success


if __name__ == '__main__':
    if initialize_system():
        app.logger.info("系统初始化成功")
    else:
        app.logger.warning("系统部分功能受限，请检查错误日志")

    app.run(host='0.0.0.0', port=5000, debug=True)