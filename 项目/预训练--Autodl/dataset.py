# -*- coding: utf-8 -*-
import os
import json
import pandas as pd
import torch
import re
import gc
import chardet
import warnings
from torch.utils.data import Dataset

try:
    import PyPDF2
    PDF_SUPPORT = True
except ImportError:
    PDF_SUPPORT = False


class MultiFormatDataset(Dataset):
    def __init__(self, tokenizer, data_dir, block_size=512, text_template=None):
        self.tokenizer = tokenizer
        self.block_size = block_size
        self.examples = []
        self.text_template = text_template or "部门：{department}\n标题：{title}\n问题：{ask}\n回答：{answer}\n"

        if not os.path.exists(data_dir):
            os.makedirs(data_dir)
            print(f"⚠️ 警告: 数据目录 {data_dir} 不存在，已创建空目录。请添加数据文件！")
            return

        def detect_encoding(file_path):
            try:
                with open(file_path, 'rb') as f:
                    result = chardet.detect(f.read())
                return result['encoding'] or 'utf-8'
            except Exception as e:
                print(f"检测文件编码时出错: {str(e)}")
                return 'utf-8'

        for filename in os.listdir(data_dir):
            file_path = os.path.join(data_dir, filename)
            print(f"📄 处理文件: {file_path}")

            try:
                encoding = detect_encoding(file_path)
                print(f"  检测到编码: {encoding}")

                if filename.lower().endswith('.csv'):
                    self._process_csv(file_path, encoding)
                elif filename.lower().endswith('.json'):
                    self._process_json(file_path, encoding)
                elif filename.lower().endswith('.txt'):
                    self._process_txt(file_path, encoding)
                elif filename.lower().endswith('.pdf') and PDF_SUPPORT:
                    self._process_pdf(file_path)
                else:
                    print(f"⚠️ 不支持的文件格式: {filename}")
                gc.collect()

            except Exception as e:
                print(f"❌ 处理文件 {filename} 时出错: {str(e)}")

        if not self.examples:
            print("⚠️ 警告: 未加载任何训练数据！请检查数据目录和文件格式。")
        else:
            print(f"✅ 成功加载 {len(self.examples)} 个训练样本")

    def _process_csv(self, file_path, encoding):
        try:
            for enc in [encoding, 'gbk', 'gb18030', 'latin1']:
                try:
                    df = pd.read_csv(file_path, encoding=enc, on_bad_lines='warn')
                    print(f"✅ 使用 {enc} 编码读取CSV文件")
                    break
                except UnicodeDecodeError:
                    continue
            else:
                print(f"❌ 无法解码 {file_path}")
                return

            for _, row in df.iterrows():
                row_dict = row.to_dict()
                try:
                    text = self.text_template.format(**row_dict)
                except KeyError:
                    text = "\n".join([f"{k}: {v}" for k, v in row_dict.items()])
                self._add_text_to_examples(text)

        except Exception as e:
            print(f"❌ CSV错误: {str(e)}")

    def _process_json(self, file_path, encoding):
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                data = json.load(f)

            if isinstance(data, list):
                for item in data:
                    self._process_json_item(item)
            elif isinstance(data, dict):
                self._process_json_item(data)

        except Exception as e:
            print(f"❌ JSON错误: {str(e)}")

    def _process_json_item(self, item):
        if isinstance(item, dict):
            try:
                text = self.text_template.format(**item)
            except KeyError:
                text = "\n".join([f"{k}: {v}" for k, v in item.items()])
            self._add_text_to_examples(text)

    def _process_txt(self, file_path, encoding):
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                text = f.read()
            paragraphs = re.split(r'\n\s*\n', text)
            for para in paragraphs:
                if para.strip():
                    self._add_text_to_examples(para.strip())
        except Exception as e:
            print(f"❌ TXT错误: {str(e)}")

    def _process_pdf(self, file_path):
        if not PDF_SUPPORT:
            print(f"❌ PDF 未启用")
            return

        try:
            with open(file_path, 'rb') as f:
                pdf_reader = PyPDF2.PdfReader(f)
                for page in pdf_reader.pages:
                    text = page.extract_text()
                    if text:
                        paragraphs = re.split(r'\n\s*\n', text)
                        for para in paragraphs:
                            if para.strip():
                                self._add_text_to_examples(para.strip())
        except Exception as e:
            print(f"❌ PDF错误: {str(e)}")

    def _add_text_to_examples(self, text):
        encoding = self.tokenizer(
            text,
            max_length=self.block_size,
            padding="max_length",
            truncation=True,
            return_tensors="pt"
        )
        input_ids = encoding['input_ids'].squeeze(0)
        self.examples.append(input_ids.cpu().clone())

    def __len__(self):
        return len(self.examples)

    def __getitem__(self, idx):
        return self.examples[idx], self.examples[idx].clone()
