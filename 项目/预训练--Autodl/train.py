import os
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, get_linear_schedule_with_warmup
from torch.utils.data import DataLoader
from torch.optim import Adam<PERSON>
from tqdm import tqdm
from dataset import MultiFormatDataset  # 自定义数据集，封装数据读取与预处理


# 查看当前 GPU 显存占用情况
def get_gpu_usage():
    if torch.cuda.is_available():
        mem = torch.cuda.memory_allocated() / 1024 ** 3  # 已分配
        reserved = torch.cuda.memory_reserved() / 1024 ** 3  # 已预留
        return f"{mem:.2f}G / {reserved:.2f}G"
    return "CPU"


def main():
    # 基本训练超参数
    gradient_accumulation_steps = 4  # 梯度累积步数，模拟更大的batch
    batch_size = 4                   # 单次迭代 batch size
    block_size = 256                 # 单条样本最大 token 长度
    epochs = 3                       # 训练轮数

    # 自动选择设备
    device = "cuda" if torch.cuda.is_available() else "cpu"

    # 预训练模型路径
    model_path = "/root/autodl-tmp/Qwen3-0.6B/Qwen/Qwen3-0.6B"

    # 加载分词器和模型
    tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        trust_remote_code=True,
        torch_dtype=torch.float32  # 使用 fp32 精度
    ).to(device)

    # 启用梯度检查点，节省显存
    model.gradient_checkpointing_enable()

    # 如 tokenizer 没有 pad token，补充
    if tokenizer.pad_token is None:
        tokenizer.add_special_tokens({'pad_token': '[PAD]'})
        model.resize_token_embeddings(len(tokenizer))

    # 构建数据集
    dataset = MultiFormatDataset(tokenizer, "./data", block_size=block_size)
    if len(dataset) == 0:
        print("❌ 没有训练数据")
        return

    # 构建 Dataloader
    dataloader = DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=True,
        pin_memory=True,
        num_workers=0
    )

    # 优化器与调度器（线性 warmup + 线性 decay）
    optimizer = AdamW(model.parameters(), lr=5e-5, weight_decay=0.01)
    total_steps = len(dataloader) * epochs // gradient_accumulation_steps
    scheduler = get_linear_schedule_with_warmup(
        optimizer,
        num_warmup_steps=max(1, total_steps // 10),
        num_training_steps=total_steps
    )

    # 设置训练模式
    model.train()
    global_step = 0  # 累计 step 统计
    accumulated_loss = 0.0  # 累计 loss
    accumulated_grad = 0     # 累计 梯度 step 统计

    # 多轮训练
    for epoch in range(epochs):
        pbar = tqdm(dataloader, desc=f"Epoch {epoch + 1}/{epochs}")

        for batch_idx, (inputs, labels) in enumerate(pbar):
            inputs, labels = inputs.to(device), labels.to(device)
            # Attention mask 掩盖 pad 部分
            attention_mask = (inputs != tokenizer.pad_token_id).float().to(device)

            # 前向传播 & 计算 loss
            outputs = model(inputs, labels=labels, attention_mask=attention_mask)
            loss = outputs.loss / gradient_accumulation_steps
            loss.backward()  # 反向传播

            # 记录损失与累积
            accumulated_loss += loss.item() * gradient_accumulation_steps
            accumulated_grad += 1

            # 每累计 x 次更新一次参数
            if accumulated_grad % gradient_accumulation_steps == 0:
                torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)  # 防止梯度爆炸
                optimizer.step()
                scheduler.step()
                optimizer.zero_grad()

                global_step += 1
                # tqdm 实时显示日志
                pbar.set_postfix({
                    "Loss": f"{accumulated_loss:.4f}",
                    "LR": f"{scheduler.get_last_lr()[0]:.2e}",
                    "GPU": get_gpu_usage(),
                    "Step": global_step
                })
                accumulated_loss = 0.0  # 累计归零

    # 保存模型
    save_dir = "/root/autodl-tmp/Qwen3-0.6B/Qwen/Qwen3-0.6B-finetune"
    model.save_pretrained(save_dir)
    tokenizer.save_pretrained(save_dir)
    print(f"✅ 训练完成，模型已保存到 {save_dir}，共训练步数 {global_step}")


if __name__ == "__main__":
    # 环境配置
    os.environ["TOKENIZERS_PARALLELISM"] = "false"  # 关闭 tokenizer 并行警告
    os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "max_split_size_mb:128"  # CUDA 显存碎片管理
    torch.backends.cudnn.benchmark = True  # 提升 cuDNN 性能
    main()
