import os
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM

def generate_text(model, tokenizer, prompt, max_length=128, device='cpu'):
    inputs = tokenizer(prompt, return_tensors="pt").to(device)
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_length=max_length,
            do_sample=True,
            top_p=0.9,
            temperature=0.8,
            eos_token_id=tokenizer.eos_token_id,
            pad_token_id=tokenizer.pad_token_id,
            num_return_sequences=1
        )
    return tokenizer.decode(outputs[0], skip_special_tokens=True)

def main():
    device = "cuda" if torch.cuda.is_available() else "cpu"
    model_dir = "/root/autodl-tmp/Qwen3-0.6B/Qwen/Qwen3-0.6B-finetune"

    print(f"🚀 Loading model from {model_dir} on {device}...")
    tokenizer = AutoTokenizer.from_pretrained(model_dir, trust_remote_code=True)
    model = AutoModelForCausalLM.from_pretrained(model_dir, trust_remote_code=True).to(device)
    model.eval()

    print("✅ Model loaded. 输入文本进行推理，输入 exit 或 quit 退出。")

    while True:
        prompt = input("\n输入文本: ")
        if prompt.strip().lower() in ["exit", "quit"]:
            break
        output = generate_text(model, tokenizer, prompt, device=device)
        print(f"\n生成结果:\n{output}")

if __name__ == "__main__":
    os.environ["TOKENIZERS_PARALLELISM"] = "false"
    main()
