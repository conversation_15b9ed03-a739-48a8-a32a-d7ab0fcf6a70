# -*- coding: utf-8 -*-
import os
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from torch.utils.data import DataLoader
from dataset import MultiFormatDataset
from tqdm import tqdm

def evaluate(model, dataloader, device):
    model.eval()
    total_loss = 0.0
    total_count = 0
    with torch.no_grad():
        for inputs, labels in tqdm(dataloader, desc="Evaluating"):
            inputs, labels = inputs.to(device), labels.to(device)
            attention_mask = (inputs != model.config.pad_token_id).float().to(device)

            outputs = model(inputs, labels=labels, attention_mask=attention_mask)
            loss = outputs.loss

            batch_size = inputs.size(0)
            total_loss += loss.item() * batch_size
            total_count += batch_size

    avg_loss = total_loss / total_count
    ppl = torch.exp(torch.tensor(avg_loss))
    return avg_loss, ppl.item()

def main():
    device = "cuda" if torch.cuda.is_available() else "cpu"
    model_dir = "/root/autodl-tmp/Qwen3-0.6B/Qwen/Qwen3-0.6B-finetune"
    eval_data_dir = "./eval_data"  # 你放验证数据的目录

    tokenizer = AutoTokenizer.from_pretrained(model_dir, trust_remote_code=True)
    model = AutoModelForCausalLM.from_pretrained(model_dir, trust_remote_code=True).to(device)

    dataset = MultiFormatDataset(tokenizer, eval_data_dir, block_size=256)
    dataloader = DataLoader(dataset, batch_size=8, shuffle=False, num_workers=0)

    avg_loss, ppl = evaluate(model, dataloader, device)
    print(f"验证集平均Loss: {avg_loss:.4f}, Perplexity: {ppl:.4f}")

if __name__ == "__main__":
    os.environ["TOKENIZERS_PARALLELISM"] = "false"
    main()
